<?php

namespace Tests\Feature\Admin\User;

use App\Http\Controllers\Admin\UserManagementController;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(UserManagementController::class)]
class UserManagementBusinessLogicTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;

    protected User $otherAdminUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);

        $this->otherAdminUser = User::factory()->create([
            'role' => 'admin',
            'name' => 'Other Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);
    }

    #[Test]
    public function admin_cannot_delete_their_own_account()
    {
        $response = $this->actingAs($this->adminUser)
            ->delete(route('admin.users.destroy', $this->adminUser));

        $response->assertStatus(302);
        $response->assertSessionHas('error', 'You cannot delete your own account.');
        $this->assertDatabaseHas('users', ['id' => $this->adminUser->id]);
    }

    #[Test]
    public function admin_can_delete_other_admin_accounts()
    {
        $response = $this->actingAs($this->adminUser)
            ->delete(route('admin.users.destroy', $this->otherAdminUser));

        $response->assertStatus(302);
        $response->assertRedirect(route('admin.users.index'));
        $response->assertSessionHas('success', "User '{$this->otherAdminUser->name}' has been deleted successfully.");
        $this->assertDatabaseMissing('users', ['id' => $this->otherAdminUser->id]);
    }

    #[Test]
    public function admin_cannot_remove_their_own_admin_role_via_update()
    {
        $updateData = [
            'name' => $this->adminUser->name,
            'email' => $this->adminUser->email,
            'username' => $this->adminUser->username,
            'role' => 'employee', // Trying to remove admin role
            'account_status' => 'active',
        ];

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $this->adminUser), $updateData);

        $response->assertStatus(302);
        $response->assertSessionHas('error', 'You cannot remove your own admin privileges.');

        $this->adminUser->refresh();
        $this->assertEquals('admin', $this->adminUser->role);
    }

    #[Test]
    public function admin_can_change_other_admin_role_via_update()
    {
        $updateData = [
            'name' => $this->otherAdminUser->name,
            'email' => $this->otherAdminUser->email,
            'username' => $this->otherAdminUser->username,
            'role' => 'employee',
            'account_status' => 'active',
        ];

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $this->otherAdminUser), $updateData);

        $response->assertStatus(302);
        $response->assertSessionHas('success', 'User updated successfully.');

        $this->otherAdminUser->refresh();
        $this->assertEquals('employee', $this->otherAdminUser->role);
    }

    #[Test]
    public function admin_cannot_remove_their_own_admin_role_via_update_role()
    {
        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.update-role', $this->adminUser), ['role' => 'employee']);

        $response->assertStatus(302);
        $response->assertSessionHas('error', 'You cannot remove your own admin privileges.');

        $this->adminUser->refresh();
        $this->assertEquals('admin', $this->adminUser->role);
    }

    #[Test]
    public function admin_can_change_other_admin_role_via_update_role()
    {
        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.update-role', $this->otherAdminUser), ['role' => 'employee']);

        $response->assertStatus(302);
        $response->assertSessionHas('success', 'User role updated successfully to employee.');

        $this->otherAdminUser->refresh();
        $this->assertEquals('employee', $this->otherAdminUser->role);
    }

    #[Test]
    public function admin_cannot_lock_their_own_account()
    {
        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.lock', $this->adminUser));

        $response->assertStatus(302);
        $response->assertSessionHas('error', 'You cannot lock your own account.');

        $this->adminUser->refresh();
        $this->assertFalse($this->adminUser->isLocked());
    }

    #[Test]
    public function admin_can_lock_other_admin_accounts()
    {
        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.lock', $this->otherAdminUser));

        $response->assertStatus(302);
        $response->assertSessionHas('success', "User '{$this->otherAdminUser->name}' has been locked for 24 hours.");

        $this->otherAdminUser->refresh();
        $this->assertTrue($this->otherAdminUser->isLocked());
        $this->assertEquals(5, $this->otherAdminUser->failed_login_attempts);
    }

    #[Test]
    public function admin_cannot_lock_their_own_account_via_update()
    {
        $updateData = [
            'name' => $this->adminUser->name,
            'email' => $this->adminUser->email,
            'username' => $this->adminUser->username,
            'role' => $this->adminUser->role,
            'account_status' => 'locked', // Trying to lock own account
        ];

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $this->adminUser), $updateData);

        $response->assertStatus(302);
        $response->assertSessionHas('error', 'You cannot lock your own account.');

        $this->adminUser->refresh();
        $this->assertFalse($this->adminUser->isLocked());
    }

    #[Test]
    public function admin_created_users_are_automatically_email_verified()
    {
        $userData = [
            'name' => 'Auto Verified User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'user',
        ];

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.store'), $userData);

        $response->assertStatus(302);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user->email_verified_at);
    }

    #[Test]
    public function user_password_is_properly_hashed_on_creation()
    {
        $userData = [
            'name' => 'Hashed Password User',
            'email' => '<EMAIL>',
            'password' => 'plainpassword123',
            'password_confirmation' => 'plainpassword123',
            'role' => 'user',
        ];

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.store'), $userData);

        $response->assertStatus(302);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotEquals('plainpassword123', $user->password);
        $this->assertTrue(Hash::check('plainpassword123', $user->password));
    }

    #[Test]
    public function user_password_is_properly_hashed_on_update()
    {
        $user = User::factory()->create([
            'role' => 'employee',
            'email' => '<EMAIL>',
            'name' => 'Hash Test User',
        ]);
        $oldPasswordHash = $user->password;

        $updateData = [
            'name' => $user->name,
            'email' => $user->email,
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'active',
            'password' => 'newplainpassword123',
            'password_confirmation' => 'newplainpassword123',
        ];

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), $updateData);

        $response->assertStatus(302);

        $user->refresh();
        $this->assertNotEquals($oldPasswordHash, $user->password);
        $this->assertNotEquals('newplainpassword123', $user->password);
        $this->assertTrue(Hash::check('newplainpassword123', $user->password));
    }

    #[Test]
    public function locking_user_sets_correct_lock_duration_and_failed_attempts()
    {
        $user = User::factory()->create([
            'locked_until' => null,
            'failed_login_attempts' => 0,
        ]);

        $beforeLock = now();

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.lock', $user));

        $afterLock = now();

        $response->assertStatus(302);

        $user->refresh();
        $this->assertTrue($user->isLocked());
        $this->assertEquals(5, $user->failed_login_attempts);

        // Check that lock duration is approximately 24 hours
        $this->assertTrue($user->locked_until->between(
            $beforeLock->addHours(23)->addMinutes(59),
            $afterLock->addHours(24)->addMinute()
        ));
    }

    #[Test]
    public function unlocking_user_resets_lock_and_failed_attempts()
    {
        $user = User::factory()->create([
            'locked_until' => now()->addHours(24),
            'failed_login_attempts' => 5,
        ]);

        $response = $this->actingAs($this->adminUser)
            ->post(route('admin.users.unlock', $user));

        $response->assertStatus(302);

        $user->refresh();
        $this->assertFalse($user->isLocked());
        $this->assertEquals(0, $user->failed_login_attempts);
        $this->assertNull($user->locked_until);
    }

    #[Test]
    public function update_can_reset_failed_login_attempts()
    {
        $user = User::factory()->create([
            'role' => 'employee',
            'email' => '<EMAIL>',
            'name' => 'Reset Test User',
            'failed_login_attempts' => 3,
            'locked_until' => now()->addMinutes(30),
        ]);

        $updateData = [
            'name' => $user->name,
            'email' => $user->email,
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'active',
            'reset_failed_attempts' => '1',
        ];

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), $updateData);

        $response->assertStatus(302);

        $user->refresh();
        $this->assertEquals(0, $user->failed_login_attempts);
        $this->assertNull($user->locked_until);
    }

    #[Test]
    public function update_can_lock_unlocked_user_via_account_status()
    {
        $user = User::factory()->create([
            'role' => 'employee',
            'email' => '<EMAIL>',
            'name' => 'Lock Test User',
            'locked_until' => null,
            'failed_login_attempts' => 0,
        ]);

        $updateData = [
            'name' => $user->name,
            'email' => $user->email,
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'locked',
        ];

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), $updateData);

        $response->assertStatus(302);

        $user->refresh();
        $this->assertTrue($user->isLocked());
        $this->assertEquals(5, $user->failed_login_attempts);
    }

    #[Test]
    public function update_can_unlock_locked_user_via_account_status()
    {
        $user = User::factory()->create([
            'role' => 'employee',
            'email' => '<EMAIL>',
            'name' => 'Unlock Test User',
            'locked_until' => now()->addHours(24),
            'failed_login_attempts' => 5,
        ]);

        $updateData = [
            'name' => $user->name,
            'email' => $user->email,
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'active',
        ];

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), $updateData);

        $response->assertStatus(302);

        $user->refresh();
        $this->assertFalse($user->isLocked());
        $this->assertEquals(0, $user->failed_login_attempts);
    }

    #[Test]
    public function all_valid_role_types_can_be_created()
    {
        $roles = ['admin', 'employee', 'user'];

        foreach ($roles as $role) {
            $userData = [
                'name' => ucfirst($role).' User',
                'email' => $role.'@test.com',
                'password' => 'password123',
                'password_confirmation' => 'password123',
                'role' => $role,
            ];

            $response = $this->actingAs($this->adminUser)
                ->post(route('admin.users.store'), $userData);

            $response->assertStatus(302);
            $this->assertDatabaseHas('users', [
                'email' => $role.'@test.com',
                'role' => $role,
            ]);
        }
    }
}
