<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\ReservationController::class)]
class ReservationControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Field $field;

    protected Utility $utility;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create(['role' => 'user']);

        // Create test field
        $this->field = Field::factory()->active()->create([
            'hourly_rate' => 50.00,
            'min_booking_hours' => 1,
            'max_booking_hours' => 4,
        ]);

        // Create test utility
        $this->utility = Utility::factory()->active()->create([
            'hourly_rate' => 10.00,
        ]);
    }

    #[Test]
    public function index_displays_user_reservations()
    {
        // Arrange
        Reservation::factory()->count(3)->forUser($this->user)->create();
        Reservation::factory()->count(2)->create(); // Other user's reservations

        // Act
        $response = $this->actingAs($this->user)->get(route('reservations.index'));

        // Assert
        $response->assertStatus(200);
        $response->assertViewIs('reservations.index');
        $response->assertViewHas('reservations');
        $response->assertViewHas('upcomingCount');
    }

    #[Test]
    public function index_calculates_upcoming_count_correctly()
    {
        // Arrange
        Reservation::factory()->upcoming()->confirmed()->forUser($this->user)->count(2)->create();
        Reservation::factory()->past()->forUser($this->user)->create();
        Reservation::factory()->upcoming()->cancelled()->forUser($this->user)->create();

        // Act
        $response = $this->actingAs($this->user)->get(route('reservations.index'));

        // Assert
        $response->assertViewHas('upcomingCount', 2);
    }

    #[Test]
    public function index_requires_authentication()
    {
        // Act
        $response = $this->get(route('reservations.index'));

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function create_displays_form_with_fields_and_utilities()
    {
        // Arrange
        Field::factory()->active()->count(2)->create();
        Utility::factory()->active()->count(2)->create();

        // Act
        $response = $this->actingAs($this->user)->get(route('reservations.create'));

        // Assert
        $response->assertStatus(200);
        $response->assertViewIs('reservations.create');
        $response->assertViewHas('fields');
        $response->assertViewHas('utilities');
    }

    #[Test]
    public function create_pre_fills_form_when_field_id_provided()
    {
        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.create', ['field_id' => $this->field->id]));

        // Assert
        $response->assertStatus(200);
        $response->assertViewHas('selectedField', $this->field);
    }

    #[Test]
    public function create_requires_authentication()
    {
        // Act
        $response = $this->get(route('reservations.create'));

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function store_creates_reservation_with_valid_data()
    {
        // Arrange
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
            'special_requests' => 'Need extra equipment',
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('bookings', [
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => $reservationData['booking_date'],
            'start_time' => '10:00',
            'end_time' => '12:00',
            'duration_hours' => 2,
            'status' => 'Confirmed', // Auto-confirmed for Phase 1
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
        ]);
    }

    #[Test]
    public function store_creates_reservation_with_utilities()
    {
        // Arrange
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
            'utilities' => [
                ['id' => $this->utility->id, 'hours' => 1],
            ],
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('success');

        $reservation = Reservation::where('user_id', $this->user->id)->first();
        $this->assertNotNull($reservation);

        // Check utility relationship
        $this->assertTrue($reservation->utilities->contains($this->utility));
    }

    #[Test]
    public function store_validates_required_fields()
    {
        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), []);

        // Assert
        $response->assertSessionHasErrors([
            'field_id',
            'booking_date',
            'start_time',
            'duration_hours',
        ]);
    }

    #[Test]
    public function store_validates_utilities_when_provided()
    {
        // Arrange
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
            'utilities' => [
                ['id' => 999, 'hours' => 1], // Non-existent utility
                ['id' => $this->utility->id, 'hours' => 0], // Invalid hours
            ],
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->post(route('reservations.store'), $reservationData);

        // Assert
        $response->assertSessionHasErrors(['utilities.0.id', 'utilities.1.hours']);
    }

    #[Test]
    public function store_requires_authentication()
    {
        // Act
        $response = $this->post(route('reservations.store'), []);

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function show_displays_reservation_for_owner()
    {
        // Arrange
        $reservation = Reservation::factory()->forUser($this->user)->create();

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.show', $reservation));

        // Assert
        $response->assertStatus(200);
        $response->assertViewIs('reservations.show');
        $response->assertViewHas('reservation', $reservation);
    }

    #[Test]
    public function show_returns_403_for_unauthorized_user()
    {
        // Arrange
        $otherUser = User::factory()->create();
        $reservation = Reservation::factory()->forUser($otherUser)->create();

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.show', $reservation));

        // Assert
        $response->assertStatus(403);
    }

    #[Test]
    public function show_requires_authentication()
    {
        // Arrange
        $reservation = Reservation::factory()->create();

        // Act
        $response = $this->get(route('reservations.show', $reservation));

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function edit_displays_form_for_modifiable_reservation()
    {
        // Arrange
        $reservation = Reservation::factory()->upcoming()->confirmed()->forUser($this->user)->create([
            'booking_date' => now()->addDays(2)->format('Y-m-d'), // More than 24 hours ahead
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.edit', $reservation));

        // Assert
        $response->assertStatus(200);
        $response->assertViewIs('reservations.edit');
        $response->assertViewHas('reservation', $reservation);
        $response->assertViewHas('fields');
        $response->assertViewHas('utilities');
    }

    #[Test]
    public function edit_returns_403_for_unauthorized_user()
    {
        // Arrange
        $otherUser = User::factory()->create();
        $reservation = Reservation::factory()->forUser($otherUser)->create();

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.edit', $reservation));

        // Assert
        $response->assertStatus(403);
    }

    #[Test]
    public function edit_redirects_for_non_modifiable_reservation()
    {
        // Arrange - Create reservation within 24-hour cutoff
        $reservation = Reservation::factory()->upcoming()->confirmed()->forUser($this->user)->create([
            'booking_date' => now()->addHours(12)->format('Y-m-d'),
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->get(route('reservations.edit', $reservation));

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('error');
        $response->assertSessionHasErrors([], 'This reservation cannot be modified. Reservations must be modified at least 24 hours in advance.');
    }

    #[Test]
    public function edit_requires_authentication()
    {
        // Arrange
        $reservation = Reservation::factory()->create();

        // Act
        $response = $this->get(route('reservations.edit', $reservation));

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function update_modifies_reservation_with_valid_data()
    {
        // Arrange
        $reservation = Reservation::factory()->upcoming()->confirmed()->forUser($this->user)->create([
            'booking_date' => now()->addDays(3)->format('Y-m-d'), // More than 24 hours ahead
            'field_id' => $this->field->id,
            'start_time' => '10:00',
            'duration_hours' => 2,
        ]);

        $updateData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(4)->format('Y-m-d'),
            'start_time' => '14:00',
            'duration_hours' => 3,
            'customer_name' => 'Updated Name',
            'special_requests' => 'Updated requests',
            'utilities' => [
                ['id' => $this->utility->id, 'hours' => 2],
            ],
        ];

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), $updateData);

        // Assert
        $response->assertRedirect(route('reservations.show', $reservation));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
            'booking_date' => $updateData['booking_date'],
            'start_time' => '14:00',
            'end_time' => '17:00',
            'duration_hours' => 3,
            'customer_name' => 'Updated Name',
            'special_requests' => 'Updated requests',
        ]);
    }

    #[Test]
    public function update_returns_403_for_unauthorized_user()
    {
        // Arrange
        $otherUser = User::factory()->create();
        $reservation = Reservation::factory()->forUser($otherUser)->create();

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), ['field_id' => $this->field->id]);

        // Assert
        $response->assertStatus(403);
    }

    #[Test]
    public function update_redirects_for_non_modifiable_reservation()
    {
        // Arrange - Create reservation within 24-hour cutoff
        $reservation = Reservation::factory()->upcoming()->confirmed()->forUser($this->user)->create([
            'booking_date' => now()->addHours(12)->format('Y-m-d'),
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), ['field_id' => $this->field->id]);

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('error');
    }

    #[Test]
    public function update_validates_required_fields()
    {
        // Arrange
        $reservation = Reservation::factory()->upcoming()->forUser($this->user)->create([
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->put(route('reservations.update', $reservation), []);

        // Assert
        $response->assertSessionHasErrors([
            'field_id',
            'booking_date',
            'start_time',
            'duration_hours',
        ]);
    }

    #[Test]
    public function update_requires_authentication()
    {
        // Arrange
        $reservation = Reservation::factory()->create();

        // Act
        $response = $this->put(route('reservations.update', $reservation), []);

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function cancel_updates_reservation_status_for_owner()
    {
        // Arrange
        $reservation = Reservation::factory()->upcoming()->confirmed()->forUser($this->user)->create([
            'booking_date' => now()->addDays(2)->format('Y-m-d'), // More than 24 hours ahead
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->patch(route('reservations.cancel', $reservation));

        // Assert
        $response->assertRedirect(route('reservations.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
            'status' => 'Cancelled',
        ]);

        $reservation->refresh();
        $this->assertNotNull($reservation->cancelled_at);
    }

    #[Test]
    public function cancel_returns_403_for_unauthorized_user()
    {
        // Arrange
        $otherUser = User::factory()->create();
        $reservation = Reservation::factory()->forUser($otherUser)->create();

        // Act
        $response = $this->actingAs($this->user)
            ->patch(route('reservations.cancel', $reservation));

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
            'status' => $reservation->status, // Original status unchanged
        ]);
    }

    #[Test]
    public function cancel_redirects_for_non_cancellable_reservation()
    {
        // Arrange - Create reservation within 24-hour cutoff
        $reservation = Reservation::factory()->upcoming()->confirmed()->forUser($this->user)->create([
            'booking_date' => now()->addHours(12)->format('Y-m-d'),
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->patch(route('reservations.cancel', $reservation));

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('error');

        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
            'status' => $reservation->status, // Original status unchanged
        ]);
    }

    #[Test]
    public function cancel_redirects_for_already_cancelled_reservation()
    {
        // Arrange
        $reservation = Reservation::factory()->cancelled()->forUser($this->user)->create([
            'status' => 'Cancelled',
        ]);

        // Act
        $response = $this->actingAs($this->user)
            ->patch(route('reservations.cancel', $reservation));

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('error');
    }

    #[Test]
    public function cancel_requires_authentication()
    {
        // Arrange
        $reservation = Reservation::factory()->create();

        // Act
        $response = $this->patch(route('reservations.cancel', $reservation));

        // Assert
        $response->assertRedirect(route('login'));
    }
}
