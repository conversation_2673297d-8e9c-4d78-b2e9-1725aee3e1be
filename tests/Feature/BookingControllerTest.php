<?php

namespace Tests\Feature;

use App\Models\Booking;
use App\Models\Field;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\BookingController::class)]
class BookingControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;

    protected User $clientUser;

    protected User $employeeUser;

    protected Field $field;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users with different roles
        $this->adminUser = User::factory()->create(['role' => 'admin']);
        $this->clientUser = User::factory()->create(['role' => 'user']);
        $this->employeeUser = User::factory()->create(['role' => 'employee']);

        // Create test field
        $this->field = Field::factory()->active()->create([
            'hourly_rate' => 50.00,
            'min_booking_hours' => 1,
            'max_booking_hours' => 4,
        ]);
    }

    #[Test]
    public function index_displays_bookings_for_authenticated_admin()
    {
        // Arrange
        Booking::factory()->count(3)->create();

        // Act
        $response = $this->actingAs($this->adminUser)->get(route('bookings.index'));

        // Assert
        $response->assertStatus(200);
        $response->assertViewIs('bookings.index');
        $response->assertViewHas('bookings');
    }

    #[Test]
    public function index_filters_bookings_for_client_users()
    {
        // Arrange
        Booking::factory()->create(['user_id' => $this->clientUser->id]);
        Booking::factory()->create(['user_id' => $this->adminUser->id]);

        // Act
        $response = $this->actingAs($this->clientUser)->get(route('bookings.index'));

        // Assert
        $response->assertStatus(200);
        $response->assertViewHas('bookings', function ($bookings) {
            return $bookings->count() === 1;
        });
    }

    #[Test]
    public function index_applies_status_filter()
    {
        // Arrange
        Booking::factory()->confirmed()->create();
        Booking::factory()->pending()->create();

        // Act
        $response = $this->actingAs($this->adminUser)
            ->get(route('bookings.index', ['status' => 'Confirmed']));

        // Assert
        $response->assertStatus(200);
        $response->assertViewHas('bookings', function ($bookings) {
            return $bookings->count() === 1;
        });
    }

    #[Test]
    public function index_requires_authentication()
    {
        // Act
        $response = $this->get(route('bookings.index'));

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function create_displays_form_for_authenticated_user()
    {
        // Act
        $response = $this->actingAs($this->clientUser)->get(route('bookings.create'));

        // Assert
        $response->assertStatus(200);
        $response->assertViewIs('bookings.create');
        $response->assertViewHas('fields');
    }

    #[Test]
    public function create_requires_authentication()
    {
        // Act
        $response = $this->get(route('bookings.create'));

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function store_creates_booking_with_valid_data()
    {
        // Arrange
        $bookingData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
            'special_requests' => 'Need extra equipment',
        ];

        // Act
        $response = $this->actingAs($this->clientUser)
            ->post(route('bookings.store'), $bookingData);

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('bookings', [
            'field_id' => $this->field->id,
            'user_id' => $this->clientUser->id,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'duration_hours' => 2,
            'total_cost' => 100.00,
            'status' => 'Pending',
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
        ]);
    }

    #[Test]
    public function store_creates_confirmed_booking_for_admin()
    {
        // Arrange
        $bookingData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
        ];

        // Act
        $response = $this->actingAs($this->adminUser)
            ->post(route('bookings.store'), $bookingData);

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('bookings', [
            'field_id' => $this->field->id,
            'user_id' => $this->adminUser->id,
            'status' => 'Confirmed',
            'booked_by' => $this->adminUser->id,
        ]);
    }

    #[Test]
    public function store_validates_required_fields()
    {
        // Act
        $response = $this->actingAs($this->clientUser)
            ->post(route('bookings.store'), []);

        // Assert
        $response->assertSessionHasErrors([
            'field_id',
            'booking_date',
            'start_time',
            'duration_hours',
        ]);
    }

    #[Test]
    public function store_validates_business_hours()
    {
        // Arrange
        $bookingData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '07:00', // Before 8 AM
            'duration_hours' => 2,
        ];

        // Act
        $response = $this->actingAs($this->clientUser)
            ->post(route('bookings.store'), $bookingData);

        // Assert
        $response->assertSessionHasErrors(['start_time']);
    }

    #[Test]
    public function store_requires_authentication()
    {
        // Act
        $response = $this->post(route('bookings.store'), []);

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function show_displays_booking_for_owner()
    {
        // Arrange
        $booking = Booking::factory()->create(['user_id' => $this->clientUser->id]);

        // Act
        $response = $this->actingAs($this->clientUser)
            ->get(route('bookings.show', $booking));

        // Assert
        $response->assertStatus(200);
        $response->assertViewIs('bookings.show');
        $response->assertViewHas('booking', $booking);
    }

    #[Test]
    public function show_displays_booking_for_admin()
    {
        // Arrange
        $booking = Booking::factory()->create(['user_id' => $this->clientUser->id]);

        // Act
        $response = $this->actingAs($this->adminUser)
            ->get(route('bookings.show', $booking));

        // Assert
        $response->assertStatus(200);
        $response->assertViewIs('bookings.show');
        $response->assertViewHas('booking', $booking);
    }

    #[Test]
    public function show_returns_403_for_unauthorized_user()
    {
        // Arrange
        $booking = Booking::factory()->create(['user_id' => $this->adminUser->id]);

        // Act
        $response = $this->actingAs($this->clientUser)
            ->get(route('bookings.show', $booking));

        // Assert
        $response->assertStatus(403);
    }

    #[Test]
    public function show_requires_authentication()
    {
        // Arrange
        $booking = Booking::factory()->create();

        // Act
        $response = $this->get(route('bookings.show', $booking));

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function edit_displays_form_for_owner()
    {
        // Arrange
        $booking = Booking::factory()->upcoming()->pending()->create([
            'user_id' => $this->clientUser->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
        ]);

        // Act
        $response = $this->actingAs($this->clientUser)
            ->get(route('bookings.edit', $booking));

        // Assert
        $response->assertStatus(200);
        $response->assertViewIs('bookings.edit');
        $response->assertViewHas('booking', $booking);
        $response->assertViewHas('fields');
    }

    #[Test]
    public function edit_returns_403_for_unauthorized_user()
    {
        // Arrange
        $booking = Booking::factory()->create(['user_id' => $this->adminUser->id]);

        // Act
        $response = $this->actingAs($this->clientUser)
            ->get(route('bookings.edit', $booking));

        // Assert
        $response->assertStatus(403);
    }

    #[Test]
    public function edit_redirects_for_past_booking()
    {
        // Arrange
        $booking = Booking::factory()->past()->create([
            'user_id' => $this->clientUser->id,
            'booking_date' => now()->subDay()->format('Y-m-d'),
        ]);

        // Act
        $response = $this->actingAs($this->clientUser)
            ->get(route('bookings.edit', $booking));

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('error', 'This booking cannot be edited.');
    }

    #[Test]
    public function edit_requires_authentication()
    {
        // Arrange
        $booking = Booking::factory()->create();

        // Act
        $response = $this->get(route('bookings.edit', $booking));

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function update_modifies_booking_with_valid_data()
    {
        // Arrange
        $booking = Booking::factory()->upcoming()->pending()->create([
            'user_id' => $this->clientUser->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
        ]);

        $updateData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
            'start_time' => '14:00',
            'duration_hours' => 3,
            'customer_name' => 'Updated Name',
            'special_requests' => 'Updated requests',
        ];

        // Act
        $response = $this->actingAs($this->clientUser)
            ->put(route('bookings.update', $booking), $updateData);

        // Assert
        $response->assertRedirect(route('bookings.show', $booking));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'start_time' => '14:00',
            'end_time' => '17:00',
            'duration_hours' => 3,
            'total_cost' => 150.00,
            'customer_name' => 'Updated Name',
            'special_requests' => 'Updated requests',
        ]);
    }

    #[Test]
    public function update_returns_403_for_unauthorized_user()
    {
        // Arrange
        $booking = Booking::factory()->create(['user_id' => $this->adminUser->id]);

        // Act
        $response = $this->actingAs($this->clientUser)
            ->put(route('bookings.update', $booking), ['field_id' => $this->field->id]);

        // Assert
        $response->assertStatus(403);
    }

    #[Test]
    public function update_validates_required_fields()
    {
        // Arrange
        $booking = Booking::factory()->upcoming()->create(['user_id' => $this->clientUser->id]);

        // Act
        $response = $this->actingAs($this->clientUser)
            ->put(route('bookings.update', $booking), []);

        // Assert
        $response->assertSessionHasErrors([
            'field_id',
            'booking_date',
            'start_time',
            'duration_hours',
        ]);
    }

    #[Test]
    public function update_requires_authentication()
    {
        // Arrange
        $booking = Booking::factory()->create();

        // Act
        $response = $this->put(route('bookings.update', $booking), []);

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function destroy_deletes_booking_for_owner()
    {
        // Arrange
        $booking = Booking::factory()->create(['user_id' => $this->clientUser->id]);

        // Act
        $response = $this->actingAs($this->clientUser)
            ->delete(route('bookings.destroy', $booking));

        // Assert
        $response->assertRedirect(route('bookings.index'));
        $response->assertSessionHas('success');
        $this->assertDatabaseMissing('bookings', ['id' => $booking->id]);
    }

    #[Test]
    public function destroy_deletes_booking_for_admin()
    {
        // Arrange
        $booking = Booking::factory()->create(['user_id' => $this->clientUser->id]);

        // Act
        $response = $this->actingAs($this->adminUser)
            ->delete(route('bookings.destroy', $booking));

        // Assert
        $response->assertRedirect(route('bookings.index'));
        $response->assertSessionHas('success');
        $this->assertDatabaseMissing('bookings', ['id' => $booking->id]);
    }

    #[Test]
    public function destroy_returns_403_for_unauthorized_user()
    {
        // Arrange
        $booking = Booking::factory()->create(['user_id' => $this->adminUser->id]);

        // Act
        $response = $this->actingAs($this->clientUser)
            ->delete(route('bookings.destroy', $booking));

        // Assert
        $response->assertStatus(403);
        $this->assertDatabaseHas('bookings', ['id' => $booking->id]);
    }

    #[Test]
    public function destroy_requires_authentication()
    {
        // Arrange
        $booking = Booking::factory()->create();

        // Act
        $response = $this->delete(route('bookings.destroy', $booking));

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function confirm_updates_booking_status_for_admin()
    {
        // Arrange
        $booking = Booking::factory()->pending()->create();

        // Act
        $response = $this->actingAs($this->adminUser)
            ->post(route('bookings.confirm', $booking));

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'Confirmed',
        ]);

        $booking->refresh();
        $this->assertNotNull($booking->confirmed_at);
    }

    #[Test]
    public function confirm_returns_403_for_non_admin()
    {
        // Arrange
        $booking = Booking::factory()->pending()->create();

        // Act
        $response = $this->actingAs($this->clientUser)
            ->post(route('bookings.confirm', $booking));

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'Pending',
        ]);
    }

    #[Test]
    public function confirm_requires_authentication()
    {
        // Arrange
        $booking = Booking::factory()->create();

        // Act
        $response = $this->post(route('bookings.confirm', $booking));

        // Assert
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function cancel_updates_booking_status_for_owner()
    {
        // Arrange
        $booking = Booking::factory()->upcoming()->confirmed()->create([
            'user_id' => $this->clientUser->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
        ]);

        // Act
        $response = $this->actingAs($this->clientUser)
            ->post(route('bookings.cancel', $booking));

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'Cancelled',
        ]);

        $booking->refresh();
        $this->assertNotNull($booking->cancelled_at);
    }

    #[Test]
    public function cancel_updates_booking_status_for_admin()
    {
        // Arrange
        $booking = Booking::factory()->upcoming()->confirmed()->create([
            'user_id' => $this->clientUser->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
        ]);

        // Act
        $response = $this->actingAs($this->adminUser)
            ->post(route('bookings.cancel', $booking));

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'Cancelled',
        ]);
    }

    #[Test]
    public function cancel_returns_403_for_unauthorized_user()
    {
        // Arrange
        $booking = Booking::factory()->create(['user_id' => $this->adminUser->id]);

        // Act
        $response = $this->actingAs($this->clientUser)
            ->post(route('bookings.cancel', $booking));

        // Assert
        $response->assertStatus(403);

        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => $booking->status, // Original status unchanged
        ]);
    }

    #[Test]
    public function cancel_redirects_with_error_for_past_booking()
    {
        // Arrange
        $booking = Booking::factory()->past()->create([
            'user_id' => $this->clientUser->id,
            'booking_date' => now()->subDay()->format('Y-m-d'),
        ]);

        // Act
        $response = $this->actingAs($this->clientUser)
            ->post(route('bookings.cancel', $booking));

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('error', 'This booking cannot be cancelled.');
    }

    #[Test]
    public function cancel_redirects_with_error_for_already_cancelled_booking()
    {
        // Arrange
        $booking = Booking::factory()->cancelled()->create([
            'user_id' => $this->clientUser->id,
            'status' => 'Cancelled',
        ]);

        // Act
        $response = $this->actingAs($this->clientUser)
            ->post(route('bookings.cancel', $booking));

        // Assert
        $response->assertRedirect();
        $response->assertSessionHas('error', 'This booking cannot be cancelled.');
    }

    #[Test]
    public function cancel_requires_authentication()
    {
        // Arrange
        $booking = Booking::factory()->create();

        // Act
        $response = $this->post(route('bookings.cancel', $booking));

        // Assert
        $response->assertRedirect(route('login'));
    }
}
