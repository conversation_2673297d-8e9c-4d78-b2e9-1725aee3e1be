<?php

namespace Tests\Unit\Controller;

use App\Http\Controllers\BookingController;
use App\Models\Field;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(BookingController::class)]
class BookingControllerTest extends TestCase
{
    use RefreshDatabase;

    protected BookingController $controller;

    protected Field $field;

    protected function setUp(): void
    {
        parent::setUp();

        $this->controller = new BookingController;

        // Create test field
        $this->field = Field::factory()->active()->create([
            'hourly_rate' => 50.00,
            'min_booking_hours' => 1,
            'max_booking_hours' => 4,
        ]);
    }

    #[Test]
    public function create_returns_view_with_fields()
    {
        // Arrange
        Field::factory()->active()->count(3)->create();
        $request = new Request;

        // Act
        $response = $this->controller->create($request);

        // Assert
        $this->assertEquals('bookings.create', $response->name());
        $this->assertArrayHasKey('fields', $response->getData());
        $this->assertCount(4, $response->getData()['fields']); // 3 + 1 from setUp
    }

    #[Test]
    public function create_pre_fills_form_when_field_id_provided()
    {
        // Arrange
        $request = new Request(['field_id' => $this->field->id]);

        // Act
        $response = $this->controller->create($request);

        // Assert
        $this->assertEquals('bookings.create', $response->name());
        $this->assertEquals($this->field->id, $response->getData()['selectedField']->id);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
