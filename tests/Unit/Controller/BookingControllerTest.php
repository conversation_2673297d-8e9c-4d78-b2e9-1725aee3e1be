<?php

namespace Tests\Unit\Controller;

use App\Http\Controllers\BookingController;
use App\Models\Booking;
use App\Models\Field;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(BookingController::class)]
class BookingControllerTest extends TestCase
{
    use RefreshDatabase;

    protected BookingController $controller;

    protected User $adminUser;

    protected User $clientUser;

    protected User $employeeUser;

    protected Field $field;

    protected function setUp(): void
    {
        parent::setUp();

        $this->controller = new BookingController;

        // Create test users with different roles
        $this->adminUser = User::factory()->create(['role' => 'admin']);
        $this->clientUser = User::factory()->create(['role' => 'user']);
        $this->employeeUser = User::factory()->create(['role' => 'employee']);

        // Create test field
        $this->field = Field::factory()->active()->create([
            'hourly_rate' => 50.00,
            'min_booking_hours' => 1,
            'max_booking_hours' => 4,
        ]);
    }

    #[Test]
    public function index_returns_view_with_bookings_for_admin()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->adminUser);
        $this->adminUser->shouldReceive('isClient')->andReturn(false);
        $this->adminUser->shouldReceive('isEmployee')->andReturn(false);

        Booking::factory()->count(3)->create();
        $request = new Request;

        // Act
        $response = $this->controller->index($request);

        // Assert
        $this->assertEquals('bookings.index', $response->name());
        $this->assertArrayHasKey('bookings', $response->getData());
    }

    #[Test]
    public function index_filters_bookings_for_client_users()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isClient')->andReturn(true);

        // Create bookings for different users
        Booking::factory()->create(['user_id' => $this->clientUser->id]);
        Booking::factory()->create(['user_id' => $this->adminUser->id]);

        $request = new Request;

        // Act
        $response = $this->controller->index($request);

        // Assert
        $this->assertEquals('bookings.index', $response->name());
        $bookings = $response->getData()['bookings'];
        $this->assertEquals(1, $bookings->total());
    }

    #[Test]
    public function index_applies_status_filter_when_provided()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->adminUser);
        $this->adminUser->shouldReceive('isClient')->andReturn(false);
        $this->adminUser->shouldReceive('isEmployee')->andReturn(false);

        Booking::factory()->confirmed()->create();
        Booking::factory()->pending()->create();

        $request = new Request(['status' => 'Confirmed']);

        // Act
        $response = $this->controller->index($request);

        // Assert
        $bookings = $response->getData()['bookings'];
        $this->assertEquals(1, $bookings->total());
    }

    #[Test]
    public function index_applies_date_filter_when_provided()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->adminUser);
        $this->adminUser->shouldReceive('isClient')->andReturn(false);
        $this->adminUser->shouldReceive('isEmployee')->andReturn(false);

        $today = now()->format('Y-m-d');
        $tomorrow = now()->addDay()->format('Y-m-d');

        Booking::factory()->create(['booking_date' => $today]);
        Booking::factory()->create(['booking_date' => $tomorrow]);

        $request = new Request(['date_from' => $tomorrow]);

        // Act
        $response = $this->controller->index($request);

        // Assert
        $bookings = $response->getData()['bookings'];
        $this->assertEquals(1, $bookings->total());
    }

    #[Test]
    public function create_returns_view_with_fields()
    {
        // Arrange
        Field::factory()->active()->count(3)->create();
        $request = new Request;

        // Act
        $response = $this->controller->create($request);

        // Assert
        $this->assertEquals('bookings.create', $response->name());
        $this->assertArrayHasKey('fields', $response->getData());
        $this->assertCount(4, $response->getData()['fields']); // 3 + 1 from setUp
    }

    #[Test]
    public function create_pre_fills_form_when_field_id_provided()
    {
        // Arrange
        $request = new Request(['field_id' => $this->field->id]);

        // Act
        $response = $this->controller->create($request);

        // Assert
        $this->assertEquals('bookings.create', $response->name());
        $this->assertEquals($this->field->id, $response->getData()['selectedField']->id);
    }

    #[Test]
    public function show_returns_view_with_booking_for_owner()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->create(['user_id' => $this->clientUser->id]);

        // Act
        $response = $this->controller->show($booking);

        // Assert
        $this->assertEquals('bookings.show', $response->name());
        $this->assertEquals($booking->id, $response->getData()['booking']->id);
    }

    #[Test]
    public function show_returns_view_with_booking_for_admin()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->adminUser);
        $this->adminUser->shouldReceive('isAdmin')->andReturn(true);

        $booking = Booking::factory()->create(['user_id' => $this->clientUser->id]);

        // Act
        $response = $this->controller->show($booking);

        // Assert
        $this->assertEquals('bookings.show', $response->name());
        $this->assertEquals($booking->id, $response->getData()['booking']->id);
    }

    #[Test]
    public function show_throws_403_for_unauthorized_user()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->create(['user_id' => $this->adminUser->id]);

        // Act & Assert
        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
        $this->expectExceptionMessage('You can only view your own bookings.');

        $this->controller->show($booking);
    }

    #[Test]
    public function edit_returns_view_with_booking_and_fields_for_owner()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->upcoming()->pending()->create([
            'user_id' => $this->clientUser->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
        ]);

        // Act
        $response = $this->controller->edit($booking);

        // Assert
        $this->assertEquals('bookings.edit', $response->name());
        $this->assertEquals($booking->id, $response->getData()['booking']->id);
        $this->assertArrayHasKey('fields', $response->getData());
    }

    #[Test]
    public function edit_throws_403_for_unauthorized_user()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->create(['user_id' => $this->adminUser->id]);

        // Act & Assert
        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
        $this->expectExceptionMessage('You can only edit your own bookings.');

        $this->controller->edit($booking);
    }

    #[Test]
    public function edit_redirects_with_error_for_past_booking()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->past()->create([
            'user_id' => $this->clientUser->id,
            'booking_date' => now()->subDay()->format('Y-m-d'),
        ]);

        // Act
        $response = $this->controller->edit($booking);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals('This booking cannot be edited.', session('error'));
    }

    #[Test]
    public function edit_redirects_with_error_for_completed_booking()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->completed()->create([
            'user_id' => $this->clientUser->id,
            'status' => 'Completed',
        ]);

        // Act
        $response = $this->controller->edit($booking);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals('This booking cannot be edited.', session('error'));
    }

    #[Test]
    public function edit_redirects_with_error_for_cancelled_booking()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->cancelled()->create([
            'user_id' => $this->clientUser->id,
            'status' => 'Cancelled',
        ]);

        // Act
        $response = $this->controller->edit($booking);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals('This booking cannot be edited.', session('error'));
    }

    #[Test]
    public function store_creates_booking_with_valid_data()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $requestData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
            'special_requests' => 'Need extra equipment',
        ];

        $request = new Request($requestData);

        // Act
        $response = $this->controller->store($request);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseHas('bookings', [
            'field_id' => $this->field->id,
            'user_id' => $this->clientUser->id,
            'booking_date' => $requestData['booking_date'],
            'start_time' => '10:00',
            'end_time' => '12:00',
            'duration_hours' => 2,
            'total_cost' => 100.00, // 2 hours * $50/hour
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
            'special_requests' => 'Need extra equipment',
        ]);
    }

    #[Test]
    public function store_returns_validation_errors_for_invalid_data()
    {
        // Arrange
        $requestData = [
            'field_id' => 999, // Non-existent field
            'booking_date' => now()->subDay()->format('Y-m-d'), // Past date
            'start_time' => 'invalid-time',
            'duration_hours' => 0, // Invalid duration
            'customer_email' => 'invalid-email',
        ];

        $request = new Request($requestData);

        // Act
        $response = $this->controller->store($request);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertSessionHasErrors([
            'field_id',
            'booking_date',
            'start_time',
            'duration_hours',
            'customer_email',
        ]);
    }

    #[Test]
    public function store_validates_field_duration_limits()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $field = Field::factory()->active()->create([
            'min_booking_hours' => 2,
            'max_booking_hours' => 4,
        ]);

        $requestData = [
            'field_id' => $field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 5, // Exceeds max
        ];

        $request = new Request($requestData);

        // Act
        $response = $this->controller->store($request);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertSessionHasErrors(['duration_hours']);
    }

    #[Test]
    public function store_validates_business_hours()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $requestData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '07:00', // Before 8 AM
            'duration_hours' => 2,
        ];

        $request = new Request($requestData);

        // Act
        $response = $this->controller->store($request);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertSessionHasErrors(['start_time']);
    }

    #[Test]
    public function store_validates_end_time_within_business_hours()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $requestData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '21:00', // 9 PM
            'duration_hours' => 2, // Would end at 11 PM (after 10 PM limit)
        ];

        $request = new Request($requestData);

        // Act
        $response = $this->controller->store($request);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertSessionHasErrors(['start_time']);
    }

    #[Test]
    public function store_sets_confirmed_status_for_admin_users()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->adminUser);
        Auth::shouldReceive('id')->andReturn($this->adminUser->id);
        $this->adminUser->shouldReceive('isAdmin')->andReturn(true);

        $requestData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
        ];

        $request = new Request($requestData);

        // Act
        $response = $this->controller->store($request);

        // Assert
        $this->assertDatabaseHas('bookings', [
            'field_id' => $this->field->id,
            'user_id' => $this->adminUser->id,
            'status' => 'Confirmed',
            'booked_by' => $this->adminUser->id,
        ]);
        $this->assertDatabaseMissing('bookings', [
            'confirmed_at' => null,
        ]);
    }

    #[Test]
    public function store_sets_pending_status_for_non_admin_users()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $requestData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
        ];

        $request = new Request($requestData);

        // Act
        $response = $this->controller->store($request);

        // Assert
        $this->assertDatabaseHas('bookings', [
            'field_id' => $this->field->id,
            'user_id' => $this->clientUser->id,
            'status' => 'Pending',
            'booked_by' => null,
            'confirmed_at' => null,
        ]);
    }

    #[Test]
    public function update_modifies_booking_with_valid_data()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->upcoming()->pending()->create([
            'user_id' => $this->clientUser->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
        ]);

        $requestData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
            'start_time' => '14:00',
            'duration_hours' => 3,
            'customer_name' => 'Updated Name',
            'special_requests' => 'Updated requests',
        ];

        $request = new Request($requestData);

        // Act
        $response = $this->controller->update($request, $booking);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'booking_date' => $requestData['booking_date'],
            'start_time' => '14:00',
            'end_time' => '17:00',
            'duration_hours' => 3,
            'total_cost' => 150.00, // 3 hours * $50/hour
            'customer_name' => 'Updated Name',
            'special_requests' => 'Updated requests',
        ]);
    }

    #[Test]
    public function update_throws_403_for_unauthorized_user()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->create(['user_id' => $this->adminUser->id]);
        $request = new Request(['field_id' => $this->field->id]);

        // Act & Assert
        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
        $this->expectExceptionMessage('You can only edit your own bookings.');

        $this->controller->update($request, $booking);
    }

    #[Test]
    public function update_returns_validation_errors_for_invalid_data()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->upcoming()->create(['user_id' => $this->clientUser->id]);

        $requestData = [
            'field_id' => 999, // Non-existent field
            'booking_date' => now()->subDay()->format('Y-m-d'), // Past date
            'start_time' => 'invalid-time',
            'duration_hours' => 0,
        ];

        $request = new Request($requestData);

        // Act
        $response = $this->controller->update($request, $booking);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertSessionHasErrors([
            'field_id',
            'booking_date',
            'start_time',
            'duration_hours',
        ]);
    }

    #[Test]
    public function destroy_deletes_booking_for_owner()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->create(['user_id' => $this->clientUser->id]);

        // Act
        $response = $this->controller->destroy($booking);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseMissing('bookings', ['id' => $booking->id]);
    }

    #[Test]
    public function destroy_deletes_booking_for_admin()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->adminUser);
        $this->adminUser->shouldReceive('isAdmin')->andReturn(true);

        $booking = Booking::factory()->create(['user_id' => $this->clientUser->id]);

        // Act
        $response = $this->controller->destroy($booking);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseMissing('bookings', ['id' => $booking->id]);
    }

    #[Test]
    public function destroy_throws_403_for_unauthorized_user()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->create(['user_id' => $this->adminUser->id]);

        // Act & Assert
        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
        $this->expectExceptionMessage('You can only delete your own bookings.');

        $this->controller->destroy($booking);
    }

    #[Test]
    public function confirm_updates_booking_status_for_admin()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->adminUser);
        $this->adminUser->shouldReceive('isAdmin')->andReturn(true);

        $booking = Booking::factory()->pending()->create();

        // Act
        $response = $this->controller->confirm($booking);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'Confirmed',
        ]);
        $this->assertDatabaseMissing('bookings', [
            'id' => $booking->id,
            'confirmed_at' => null,
        ]);
    }

    #[Test]
    public function confirm_throws_403_for_non_admin_user()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->pending()->create();

        // Act & Assert
        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
        $this->expectExceptionMessage('Only administrators can confirm bookings.');

        $this->controller->confirm($booking);
    }

    #[Test]
    public function cancel_updates_booking_status_for_owner()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->upcoming()->confirmed()->create([
            'user_id' => $this->clientUser->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
        ]);

        // Act
        $response = $this->controller->cancel($booking);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'Cancelled',
        ]);
        $this->assertDatabaseMissing('bookings', [
            'id' => $booking->id,
            'cancelled_at' => null,
        ]);
    }

    #[Test]
    public function cancel_updates_booking_status_for_admin()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->adminUser);
        $this->adminUser->shouldReceive('isAdmin')->andReturn(true);

        $booking = Booking::factory()->upcoming()->confirmed()->create([
            'user_id' => $this->clientUser->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
        ]);

        // Act
        $response = $this->controller->cancel($booking);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseHas('bookings', [
            'id' => $booking->id,
            'status' => 'Cancelled',
        ]);
    }

    #[Test]
    public function cancel_throws_403_for_unauthorized_user()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->create(['user_id' => $this->adminUser->id]);

        // Act & Assert
        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
        $this->expectExceptionMessage('You can only cancel your own bookings.');

        $this->controller->cancel($booking);
    }

    #[Test]
    public function cancel_redirects_with_error_for_past_booking()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->past()->create([
            'user_id' => $this->clientUser->id,
            'booking_date' => now()->subDay()->format('Y-m-d'),
        ]);

        // Act
        $response = $this->controller->cancel($booking);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals('This booking cannot be cancelled.', session('error'));
    }

    #[Test]
    public function cancel_redirects_with_error_for_already_cancelled_booking()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->cancelled()->create([
            'user_id' => $this->clientUser->id,
            'status' => 'Cancelled',
        ]);

        // Act
        $response = $this->controller->cancel($booking);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals('This booking cannot be cancelled.', session('error'));
    }

    #[Test]
    public function cancel_redirects_with_error_for_completed_booking()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->clientUser);
        Auth::shouldReceive('id')->andReturn($this->clientUser->id);
        $this->clientUser->shouldReceive('isAdmin')->andReturn(false);

        $booking = Booking::factory()->completed()->create([
            'user_id' => $this->clientUser->id,
            'status' => 'Completed',
        ]);

        // Act
        $response = $this->controller->cancel($booking);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals('This booking cannot be cancelled.', session('error'));
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
