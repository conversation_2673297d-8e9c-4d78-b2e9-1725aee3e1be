<?php

namespace Tests\Unit\Controller;

use App\Http\Controllers\ReservationController;
use App\Models\Field;
use App\Models\Utility;
use App\Services\FieldAvailabilityService;
use App\Services\ReservationCostService;
use App\Services\ReservationValidationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(ReservationController::class)]
class ReservationControllerTest extends TestCase
{
    use RefreshDatabase;

    protected ReservationController $controller;
    protected Field $field;
    protected Utility $utility;
    protected FieldAvailabilityService $availabilityService;
    protected ReservationCostService $costService;
    protected ReservationValidationService $validationService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test field
        $this->field = Field::factory()->active()->create([
            'hourly_rate' => 50.00,
            'min_booking_hours' => 1,
            'max_booking_hours' => 4
        ]);
        
        // Create test utility
        $this->utility = Utility::factory()->active()->create([
            'hourly_rate' => 10.00
        ]);
        
        // Mock services
        $this->availabilityService = Mockery::mock(FieldAvailabilityService::class);
        $this->costService = Mockery::mock(ReservationCostService::class);
        $this->validationService = Mockery::mock(ReservationValidationService::class);
        
        // Create controller with mocked services
        $this->controller = new ReservationController(
            $this->availabilityService,
            $this->costService,
            $this->validationService
        );
    }

    #[Test]
    public function create_returns_view_with_fields_and_utilities()
    {
        // Arrange
        Field::factory()->active()->count(2)->create();
        Utility::factory()->active()->count(2)->create();
        
        // Mock the availability service call
        $this->availabilityService->shouldReceive('getFieldAvailabilityCalendar')
            ->andReturn([]);
        
        $request = new Request();

        // Act
        $response = $this->controller->create($request);

        // Assert
        $this->assertEquals('reservations.create', $response->name());
        $this->assertArrayHasKey('fields', $response->getData());
        $this->assertArrayHasKey('utilities', $response->getData());
        
        $fields = $response->getData()['fields'];
        $utilities = $response->getData()['utilities'];
        
        $this->assertCount(3, $fields); // 2 + 1 from setUp
        $this->assertCount(3, $utilities); // 2 + 1 from setUp
    }

    #[Test]
    public function create_pre_fills_form_when_field_id_provided()
    {
        // Arrange
        $this->availabilityService->shouldReceive('getFieldAvailabilityCalendar')
            ->andReturn([]);
            
        $request = new Request(['field_id' => $this->field->id]);

        // Act
        $response = $this->controller->create($request);

        // Assert
        $this->assertEquals('reservations.create', $response->name());
        $this->assertEquals($this->field->id, $response->getData()['selectedField']->id);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
