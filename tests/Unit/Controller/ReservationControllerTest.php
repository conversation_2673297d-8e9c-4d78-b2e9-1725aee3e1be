<?php

namespace Tests\Unit\Controller;

use App\Http\Controllers\ReservationController;
use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use App\Models\Utility;
use App\Services\FieldAvailabilityService;
use App\Services\ReservationCostService;
use App\Services\ReservationValidationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(ReservationController::class)]
class ReservationControllerTest extends TestCase
{
    use RefreshDatabase;

    protected ReservationController $controller;

    protected User $user;

    protected Field $field;

    protected Utility $utility;

    protected FieldAvailabilityService $availabilityService;

    protected ReservationCostService $costService;

    protected ReservationValidationService $validationService;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create(['role' => 'user']);

        // Create test field
        $this->field = Field::factory()->active()->create([
            'hourly_rate' => 50.00,
            'min_booking_hours' => 1,
            'max_booking_hours' => 4,
        ]);

        // Create test utility
        $this->utility = Utility::factory()->active()->create([
            'hourly_rate' => 10.00,
        ]);

        // Mock services
        $this->availabilityService = Mockery::mock(FieldAvailabilityService::class);
        $this->costService = Mockery::mock(ReservationCostService::class);
        $this->validationService = Mockery::mock(ReservationValidationService::class);

        // Create controller with mocked services
        $this->controller = new ReservationController(
            $this->availabilityService,
            $this->costService,
            $this->validationService
        );
    }

    #[Test]
    public function index_returns_view_with_user_reservations()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->user);

        Reservation::factory()->count(3)->forUser($this->user)->create();
        Reservation::factory()->count(2)->create(); // Other user's reservations

        $request = new Request;

        // Act
        $response = $this->controller->index($request);

        // Assert
        $this->assertEquals('reservations.index', $response->name());
        $this->assertArrayHasKey('reservations', $response->getData());
        $this->assertArrayHasKey('upcomingCount', $response->getData());

        $reservations = $response->getData()['reservations'];
        $this->assertEquals(3, $reservations->total());
    }

    #[Test]
    public function index_calculates_upcoming_reservations_count()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->user);

        // Create upcoming active reservations
        Reservation::factory()->upcoming()->confirmed()->forUser($this->user)->count(2)->create();
        // Create past reservations
        Reservation::factory()->past()->forUser($this->user)->create();
        // Create cancelled reservations
        Reservation::factory()->upcoming()->cancelled()->forUser($this->user)->create();

        $request = new Request;

        // Act
        $response = $this->controller->index($request);

        // Assert
        $upcomingCount = $response->getData()['upcomingCount'];
        $this->assertEquals(2, $upcomingCount);
    }

    #[Test]
    public function create_returns_view_with_fields_and_utilities()
    {
        // Arrange
        Field::factory()->active()->count(3)->create();
        Utility::factory()->active()->count(2)->create();

        $request = new Request;

        // Act
        $response = $this->controller->create($request);

        // Assert
        $this->assertEquals('reservations.create', $response->name());
        $this->assertArrayHasKey('fields', $response->getData());
        $this->assertArrayHasKey('utilities', $response->getData());

        $fields = $response->getData()['fields'];
        $utilities = $response->getData()['utilities'];

        $this->assertCount(4, $fields); // 3 + 1 from setUp
        $this->assertCount(3, $utilities); // 2 + 1 from setUp
    }

    #[Test]
    public function create_pre_fills_form_when_field_id_provided()
    {
        // Arrange
        $request = new Request(['field_id' => $this->field->id]);

        // Act
        $response = $this->controller->create($request);

        // Assert
        $this->assertEquals('reservations.create', $response->name());
        $this->assertEquals($this->field->id, $response->getData()['selectedField']->id);
    }

    #[Test]
    public function show_returns_view_with_reservation_for_owner()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->user);
        Auth::shouldReceive('id')->andReturn($this->user->id);

        $reservation = Reservation::factory()->forUser($this->user)->create();

        // Act
        $response = $this->controller->show($reservation);

        // Assert
        $this->assertEquals('reservations.show', $response->name());
        $this->assertEquals($reservation->id, $response->getData()['reservation']->id);
    }

    #[Test]
    public function show_throws_403_for_unauthorized_user()
    {
        // Arrange
        $otherUser = User::factory()->create();
        Auth::shouldReceive('user')->andReturn($this->user);
        Auth::shouldReceive('id')->andReturn($this->user->id);

        $reservation = Reservation::factory()->forUser($otherUser)->create();

        // Act & Assert
        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
        $this->expectExceptionMessage('You can only view your own reservations.');

        $this->controller->show($reservation);
    }

    #[Test]
    public function edit_returns_view_with_reservation_and_data_for_owner()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->user);
        Auth::shouldReceive('id')->andReturn($this->user->id);

        $reservation = Reservation::factory()->upcoming()->confirmed()->forUser($this->user)->create([
            'booking_date' => now()->addDays(2)->format('Y-m-d'), // More than 24 hours ahead
        ]);

        // Act
        $response = $this->controller->edit($reservation);

        // Assert
        $this->assertEquals('reservations.edit', $response->name());
        $this->assertEquals($reservation->id, $response->getData()['reservation']->id);
        $this->assertArrayHasKey('fields', $response->getData());
        $this->assertArrayHasKey('utilities', $response->getData());
    }

    #[Test]
    public function edit_throws_403_for_unauthorized_user()
    {
        // Arrange
        $otherUser = User::factory()->create();
        Auth::shouldReceive('user')->andReturn($this->user);
        Auth::shouldReceive('id')->andReturn($this->user->id);

        $reservation = Reservation::factory()->forUser($otherUser)->create();

        // Act & Assert
        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
        $this->expectExceptionMessage('You can only edit your own reservations.');

        $this->controller->edit($reservation);
    }

    #[Test]
    public function edit_redirects_with_error_for_non_modifiable_reservation()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->user);
        Auth::shouldReceive('id')->andReturn($this->user->id);

        // Create reservation within 24-hour cutoff
        $reservation = Reservation::factory()->upcoming()->confirmed()->forUser($this->user)->create([
            'booking_date' => now()->addHours(12)->format('Y-m-d'), // Less than 24 hours ahead
        ]);

        // Act
        $response = $this->controller->edit($reservation);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals('This reservation cannot be modified. Reservations must be modified at least 24 hours in advance.', session('error'));
    }

    #[Test]
    public function cancel_updates_reservation_status_for_owner()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->user);
        Auth::shouldReceive('id')->andReturn($this->user->id);

        $reservation = Reservation::factory()->upcoming()->confirmed()->forUser($this->user)->create([
            'booking_date' => now()->addDays(2)->format('Y-m-d'),
        ]);

        // Act
        $response = $this->controller->cancel($reservation);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
            'status' => 'Cancelled',
        ]);
        $this->assertDatabaseMissing('bookings', [
            'id' => $reservation->id,
            'cancelled_at' => null,
        ]);
    }

    #[Test]
    public function cancel_throws_403_for_unauthorized_user()
    {
        // Arrange
        $otherUser = User::factory()->create();
        Auth::shouldReceive('user')->andReturn($this->user);
        Auth::shouldReceive('id')->andReturn($this->user->id);

        $reservation = Reservation::factory()->forUser($otherUser)->create();

        // Act & Assert
        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
        $this->expectExceptionMessage('You can only cancel your own reservations.');

        $this->controller->cancel($reservation);
    }

    #[Test]
    public function cancel_redirects_with_error_for_non_cancellable_reservation()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->user);
        Auth::shouldReceive('id')->andReturn($this->user->id);

        // Create reservation within 24-hour cutoff
        $reservation = Reservation::factory()->upcoming()->confirmed()->forUser($this->user)->create([
            'booking_date' => now()->addHours(12)->format('Y-m-d'),
        ]);

        // Act
        $response = $this->controller->cancel($reservation);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('cannot be cancelled', session('error'));
    }

    #[Test]
    public function store_creates_reservation_with_valid_data()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->user);
        Auth::shouldReceive('id')->andReturn($this->user->id);
        $this->user->shouldReceive('name')->andReturn('Test User');
        $this->user->shouldReceive('email')->andReturn('<EMAIL>');

        $requestData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
            'special_requests' => 'Need extra equipment',
            'utilities' => [
                ['id' => $this->utility->id, 'hours' => 1],
            ],
        ];

        // Mock validation service
        $this->validationService->shouldReceive('validateReservationCreation')
            ->with($requestData, $this->user->id)
            ->andReturn([]);

        // Mock availability service
        $this->availabilityService->shouldReceive('isFieldAvailable')
            ->andReturn(true);

        // Mock cost service
        $this->costService->shouldReceive('calculateUtilityCosts')
            ->andReturn([
                [
                    'utility_id' => $this->utility->id,
                    'hours' => 1,
                    'rate' => 10.00,
                    'cost' => 10.00,
                ],
            ]);

        $request = new Request($requestData);

        // Act
        $response = $this->controller->store($request);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseHas('bookings', [
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => $requestData['booking_date'],
            'start_time' => '10:00',
            'end_time' => '12:00',
            'duration_hours' => 2,
            'status' => 'Confirmed', // Auto-confirmed for Phase 1
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
            'special_requests' => 'Need extra equipment',
        ]);
    }

    #[Test]
    public function store_returns_validation_errors_for_invalid_data()
    {
        // Arrange
        $requestData = [
            'field_id' => 999, // Non-existent field
            'booking_date' => now()->subDay()->format('Y-m-d'), // Past date
            'start_time' => 'invalid-time',
            'duration_hours' => 0, // Invalid duration
            'customer_email' => 'invalid-email',
        ];

        $request = new Request($requestData);

        // Act
        $response = $this->controller->store($request);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertSessionHasErrors([
            'field_id',
            'booking_date',
            'start_time',
            'duration_hours',
            'customer_email',
        ]);
    }

    #[Test]
    public function store_validates_utilities_when_provided()
    {
        // Arrange
        $requestData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
            'utilities' => [
                ['id' => 999, 'hours' => 1], // Non-existent utility
                ['id' => $this->utility->id, 'hours' => 0], // Invalid hours
            ],
        ];

        $request = new Request($requestData);

        // Act
        $response = $this->controller->store($request);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertSessionHasErrors(['utilities.0.id', 'utilities.1.hours']);
    }

    #[Test]
    public function store_handles_business_rule_validation_errors()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->user);
        Auth::shouldReceive('id')->andReturn($this->user->id);

        $requestData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
        ];

        // Mock validation service to return errors
        $this->validationService->shouldReceive('validateReservationCreation')
            ->with($requestData, $this->user->id)
            ->andReturn([
                'booking_date' => ['You can only make up to 2 reservations per day.'],
            ]);

        $request = new Request($requestData);

        // Act
        $response = $this->controller->store($request);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertSessionHasErrors(['booking_date']);
    }

    #[Test]
    public function store_handles_field_availability_conflicts()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->user);
        Auth::shouldReceive('id')->andReturn($this->user->id);

        $requestData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
        ];

        // Mock validation service to pass
        $this->validationService->shouldReceive('validateReservationCreation')
            ->andReturn([]);

        // Mock availability service to return conflict
        $this->availabilityService->shouldReceive('isFieldAvailable')
            ->andReturn(false);

        $request = new Request($requestData);

        // Act
        $response = $this->controller->store($request);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertSessionHasErrors(['start_time']);
    }

    #[Test]
    public function update_modifies_reservation_with_valid_data()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->user);
        Auth::shouldReceive('id')->andReturn($this->user->id);
        $this->user->shouldReceive('name')->andReturn('Test User');
        $this->user->shouldReceive('email')->andReturn('<EMAIL>');

        $reservation = Reservation::factory()->upcoming()->confirmed()->forUser($this->user)->create([
            'booking_date' => now()->addDays(3)->format('Y-m-d'), // More than 24 hours ahead
            'field_id' => $this->field->id,
            'start_time' => '10:00',
            'duration_hours' => 2,
        ]);

        $requestData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(4)->format('Y-m-d'),
            'start_time' => '14:00',
            'duration_hours' => 3,
            'customer_name' => 'Updated Name',
            'special_requests' => 'Updated requests',
            'utilities' => [
                ['id' => $this->utility->id, 'hours' => 2],
            ],
        ];

        // Mock validation service
        $this->validationService->shouldReceive('validateReservationData')
            ->with($requestData, $reservation->id)
            ->andReturn([]);

        // Mock availability service
        $this->availabilityService->shouldReceive('isFieldAvailable')
            ->andReturn(true);

        // Mock cost service
        $this->costService->shouldReceive('calculateUtilityCosts')
            ->andReturn([
                [
                    'utility_id' => $this->utility->id,
                    'hours' => 2,
                    'rate' => 10.00,
                    'cost' => 20.00,
                ],
            ]);

        $request = new Request($requestData);

        // Act
        $response = $this->controller->update($request, $reservation);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseHas('bookings', [
            'id' => $reservation->id,
            'booking_date' => $requestData['booking_date'],
            'start_time' => '14:00',
            'end_time' => '17:00',
            'duration_hours' => 3,
            'total_cost' => 150.00, // 3 hours * $50/hour
            'customer_name' => 'Updated Name',
            'special_requests' => 'Updated requests',
        ]);
    }

    #[Test]
    public function update_throws_403_for_unauthorized_user()
    {
        // Arrange
        $otherUser = User::factory()->create();
        Auth::shouldReceive('user')->andReturn($this->user);
        Auth::shouldReceive('id')->andReturn($this->user->id);

        $reservation = Reservation::factory()->forUser($otherUser)->create();
        $request = new Request(['field_id' => $this->field->id]);

        // Act & Assert
        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
        $this->expectExceptionMessage('You can only edit your own reservations.');

        $this->controller->update($request, $reservation);
    }

    #[Test]
    public function update_redirects_with_error_for_non_modifiable_reservation()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->user);
        Auth::shouldReceive('id')->andReturn($this->user->id);

        // Create reservation within 24-hour cutoff
        $reservation = Reservation::factory()->upcoming()->confirmed()->forUser($this->user)->create([
            'booking_date' => now()->addHours(12)->format('Y-m-d'),
        ]);

        $request = new Request(['field_id' => $this->field->id]);

        // Act
        $response = $this->controller->update($request, $reservation);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('cannot be modified', session('error'));
    }

    #[Test]
    public function update_returns_validation_errors_for_invalid_data()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->user);
        Auth::shouldReceive('id')->andReturn($this->user->id);

        $reservation = Reservation::factory()->upcoming()->forUser($this->user)->create([
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
        ]);

        $requestData = [
            'field_id' => 999, // Non-existent field
            'booking_date' => now()->subDay()->format('Y-m-d'), // Past date
            'start_time' => 'invalid-time',
            'duration_hours' => 0,
        ];

        $request = new Request($requestData);

        // Act
        $response = $this->controller->update($request, $reservation);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertSessionHasErrors([
            'field_id',
            'booking_date',
            'start_time',
            'duration_hours',
        ]);
    }

    #[Test]
    public function update_handles_business_rule_validation_errors()
    {
        // Arrange
        Auth::shouldReceive('user')->andReturn($this->user);
        Auth::shouldReceive('id')->andReturn($this->user->id);

        $reservation = Reservation::factory()->upcoming()->forUser($this->user)->create([
            'booking_date' => now()->addDays(3)->format('Y-m-d'),
        ]);

        $requestData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration_hours' => 2,
        ];

        // Mock validation service to return errors
        $this->validationService->shouldReceive('validateReservationData')
            ->with($requestData, $reservation->id)
            ->andReturn([
                'start_time' => ['This time slot is already booked.'],
            ]);

        $request = new Request($requestData);

        // Act
        $response = $this->controller->update($request, $reservation);

        // Assert
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertSessionHasErrors(['start_time']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
