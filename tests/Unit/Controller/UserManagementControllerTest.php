<?php

namespace Tests\Unit\Http\Controllers\Admin;

use App\Http\Controllers\Admin\UserManagementController;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Mockery;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(UserManagementController::class)]
class UserManagementControllerTest extends TestCase
{
    use RefreshDatabase;

    protected UserManagementController $controller;

    protected User $adminUser;

    protected User $employeeUser;

    protected User $regularUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->controller = new UserManagementController;

        // Create test users with different roles
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'name' => 'Admin User',
            'email' => '<EMAIL>',
        ]);

        $this->employeeUser = User::factory()->create([
            'role' => 'employee',
            'name' => 'Employee User',
            'email' => '<EMAIL>',
        ]);

        $this->regularUser = User::factory()->create([
            'role' => 'user',
            'name' => 'Regular User',
            'email' => '<EMAIL>',
        ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    #[Test]
    public function index_returns_view_with_paginated_users()
    {
        // Create additional users for pagination testing
        User::factory()->count(20)->create();

        $response = $this->controller->index();

        $this->assertEquals('admin.users.index', $response->name());
        $this->assertArrayHasKey('users', $response->getData());

        $users = $response->getData()['users'];
        $this->assertInstanceOf(\Illuminate\Pagination\LengthAwarePaginator::class, $users);
        $this->assertEquals(15, $users->perPage()); // Default pagination size
    }

    #[Test]
    public function index_orders_users_by_created_at_desc()
    {
        // Clear existing users first
        User::query()->delete();

        // Create users with specific timestamps
        $oldUser = User::factory()->create(['created_at' => now()->subDays(2)]);
        $newUser = User::factory()->create(['created_at' => now()->subDay()]);
        $newestUser = User::factory()->create(['created_at' => now()]);

        $response = $this->controller->index();
        $users = $response->getData()['users'];

        // Check that newest user comes first
        $this->assertEquals($newestUser->id, $users->first()->id);
    }

    #[Test]
    public function create_returns_create_view()
    {
        $response = $this->controller->create();

        $this->assertEquals('admin.users.create', $response->name());
    }

    #[Test]
    public function show_returns_view_with_user()
    {
        $user = User::factory()->create();

        $response = $this->controller->show($user);

        $this->assertEquals('admin.users.show', $response->name());
        $this->assertArrayHasKey('user', $response->getData());
        $this->assertEquals($user->id, $response->getData()['user']->id);
    }

    #[Test]
    public function edit_returns_view_with_user()
    {
        $user = User::factory()->create();

        $response = $this->controller->edit($user);

        $this->assertEquals('admin.users.edit', $response->name());
        $this->assertArrayHasKey('user', $response->getData());
        $this->assertEquals($user->id, $response->getData()['user']->id);
    }

    #[Test]
    public function store_creates_user_with_valid_data()
    {
        $userData = [
            'name' => 'New User',
            'email' => '<EMAIL>',
            'username' => 'newuser',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'employee',
        ];

        $request = new Request($userData);
        $response = $this->controller->store($request);

        $this->assertDatabaseHas('users', [
            'name' => 'New User',
            'email' => '<EMAIL>',
            'username' => 'newuser',
            'role' => 'employee',
        ]);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user->email_verified_at); // Auto-verified
        $this->assertTrue(Hash::check('password123', $user->password));

        // Check redirect response
        $this->assertEquals(302, $response->getStatusCode());
    }

    #[Test]
    public function store_creates_user_without_username()
    {
        $userData = [
            'name' => 'User Without Username',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'user',
        ];

        $request = new Request($userData);
        $response = $this->controller->store($request);

        $this->assertDatabaseHas('users', [
            'name' => 'User Without Username',
            'email' => '<EMAIL>',
            'username' => null,
            'role' => 'user',
        ]);
    }

    #[Test]
    public function store_validates_required_fields()
    {
        $userData = [
            'email' => '<EMAIL>',
            // Missing required fields: name, password, role
        ];

        $request = new Request($userData);
        $response = $this->controller->store($request);

        // Should redirect back with errors
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    #[Test]
    public function store_validates_unique_email()
    {
        $existingUser = User::factory()->create(['email' => '<EMAIL>']);

        $userData = [
            'name' => 'Duplicate Email User',
            'email' => '<EMAIL>', // Duplicate email
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'user',
        ];

        $request = new Request($userData);
        $response = $this->controller->store($request);

        // Should redirect back with errors
        $this->assertEquals(302, $response->getStatusCode());

        // Should not create a new user
        $this->assertEquals(1, User::where('email', '<EMAIL>')->count());
    }

    #[Test]
    public function store_validates_unique_username()
    {
        $existingUser = User::factory()->create(['username' => 'existinguser']);

        $userData = [
            'name' => 'Duplicate Username User',
            'email' => '<EMAIL>',
            'username' => 'existinguser', // Duplicate username
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'user',
        ];

        $request = new Request($userData);
        $response = $this->controller->store($request);

        // Should redirect back with errors
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    #[Test]
    public function store_validates_password_confirmation()
    {
        $userData = [
            'name' => 'Password Mismatch User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'differentpassword',
            'role' => 'user',
        ];

        $request = new Request($userData);
        $response = $this->controller->store($request);

        // Should redirect back with errors
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    #[Test]
    public function store_validates_role_values()
    {
        $userData = [
            'name' => 'Invalid Role User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'invalid_role', // Invalid role
        ];

        $request = new Request($userData);
        $response = $this->controller->store($request);

        // Should redirect back with errors
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    #[Test]
    public function store_validates_username_alpha_dash()
    {
        $userData = [
            'name' => 'Invalid Username User',
            'email' => '<EMAIL>',
            'username' => 'invalid username!', // Contains spaces and special chars
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'user',
        ];

        $request = new Request($userData);
        $response = $this->controller->store($request);

        // Should redirect back with errors
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    #[Test]
    public function update_role_updates_user_role_successfully()
    {
        $user = User::factory()->create(['role' => 'user']);

        $requestData = ['role' => 'employee'];
        $request = new Request($requestData);

        $response = $this->controller->updateRole($request, $user);

        $user->refresh();
        $this->assertEquals('employee', $user->role);
        $this->assertEquals(302, $response->getStatusCode());
    }

    #[Test]
    public function update_role_validates_role_values()
    {
        $user = User::factory()->create(['role' => 'user']);

        $requestData = ['role' => 'invalid_role'];
        $request = new Request($requestData);

        $response = $this->controller->updateRole($request, $user);

        $user->refresh();
        $this->assertEquals('user', $user->role); // Should remain unchanged
        $this->assertEquals(302, $response->getStatusCode());
    }

    #[Test]
    public function update_role_prevents_self_admin_removal()
    {
        // Mock the authenticated user
        Auth::shouldReceive('id')->andReturn($this->adminUser->id);

        $requestData = ['role' => 'employee'];
        $request = new Request($requestData);

        $response = $this->controller->updateRole($request, $this->adminUser);

        $this->adminUser->refresh();
        $this->assertEquals('admin', $this->adminUser->role); // Should remain admin
        $this->assertEquals(302, $response->getStatusCode());
    }

    #[Test]
    public function update_role_allows_admin_to_change_other_admin_role()
    {
        $otherAdmin = User::factory()->create(['role' => 'admin']);

        // Mock the authenticated user as different admin
        Auth::shouldReceive('id')->andReturn($this->adminUser->id);

        $requestData = ['role' => 'employee'];
        $request = new Request($requestData);

        $response = $this->controller->updateRole($request, $otherAdmin);

        $otherAdmin->refresh();
        $this->assertEquals('employee', $otherAdmin->role);
        $this->assertEquals(302, $response->getStatusCode());
    }

    #[Test]
    public function update_updates_user_basic_information()
    {
        $user = User::factory()->create([
            'name' => 'Old Name',
            'email' => '<EMAIL>',
            'username' => 'oldusername',
            'role' => 'user',
        ]);

        $requestData = [
            'name' => 'New Name',
            'email' => '<EMAIL>',
            'username' => 'newusername',
            'role' => 'employee',
            'account_status' => 'active',
        ];
        $request = new Request($requestData);

        $response = $this->controller->update($request, $user);

        $user->refresh();
        $this->assertEquals('New Name', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('newusername', $user->username);
        $this->assertEquals('employee', $user->role);
        $this->assertEquals(302, $response->getStatusCode());
    }

    #[Test]
    public function update_updates_password_when_provided()
    {
        $user = User::factory()->create([
            'role' => 'employee',
            'email' => '<EMAIL>',
            'name' => 'Test User',
        ]);
        $oldPasswordHash = $user->password;

        $requestData = [
            'name' => $user->name,
            'email' => $user->email,
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'active',
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123',
        ];
        $request = new Request($requestData);

        $response = $this->controller->update($request, $user);

        $user->refresh();
        $this->assertNotEquals($oldPasswordHash, $user->password);
        $this->assertTrue(Hash::check('newpassword123', $user->password));
    }

    #[Test]
    public function update_does_not_update_password_when_not_provided()
    {
        $user = User::factory()->create([
            'role' => 'employee',
            'email' => '<EMAIL>',
            'name' => 'Test User 2',
        ]);
        $oldPasswordHash = $user->password;

        $requestData = [
            'name' => 'Updated Name',
            'email' => $user->email,
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'active',
        ];
        $request = new Request($requestData);

        $response = $this->controller->update($request, $user);

        $user->refresh();
        $this->assertEquals($oldPasswordHash, $user->password);
        $this->assertEquals('Updated Name', $user->name);
    }

    #[Test]
    public function update_locks_user_account_when_status_is_locked()
    {
        $user = User::factory()->create([
            'role' => 'employee',
            'email' => '<EMAIL>',
            'name' => 'Test User 3',
            'locked_until' => null,
            'failed_login_attempts' => 0,
        ]);

        $requestData = [
            'name' => $user->name,
            'email' => $user->email,
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'locked',
        ];
        $request = new Request($requestData);

        $response = $this->controller->update($request, $user);

        $user->refresh();
        $this->assertTrue($user->isLocked());
        $this->assertEquals(5, $user->failed_login_attempts);
    }

    #[Test]
    public function update_unlocks_user_account_when_status_is_active()
    {
        $user = User::factory()->create([
            'role' => 'employee',
            'email' => '<EMAIL>',
            'name' => 'Test User 4',
            'locked_until' => now()->addHours(24),
            'failed_login_attempts' => 5,
        ]);

        $requestData = [
            'name' => $user->name,
            'email' => $user->email,
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'active',
        ];
        $request = new Request($requestData);

        $response = $this->controller->update($request, $user);

        $user->refresh();
        $this->assertFalse($user->isLocked());
        $this->assertEquals(0, $user->failed_login_attempts);
    }

    #[Test]
    public function update_resets_failed_attempts_when_requested()
    {
        $user = User::factory()->create([
            'role' => 'employee',
            'email' => '<EMAIL>',
            'name' => 'Test User 5',
            'failed_login_attempts' => 3,
            'locked_until' => now()->addMinutes(30),
        ]);

        $requestData = [
            'name' => $user->name,
            'email' => $user->email,
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'active',
            'reset_failed_attempts' => true,
        ];
        $request = new Request($requestData);

        $response = $this->controller->update($request, $user);

        $user->refresh();
        $this->assertEquals(0, $user->failed_login_attempts);
        $this->assertNull($user->locked_until);
    }

    #[Test]
    public function update_prevents_self_admin_removal()
    {
        // Mock the authenticated user
        Auth::shouldReceive('id')->andReturn($this->adminUser->id);

        $requestData = [
            'name' => $this->adminUser->name,
            'email' => $this->adminUser->email,
            'username' => $this->adminUser->username,
            'role' => 'employee', // Trying to remove admin role
            'account_status' => 'active',
        ];
        $request = new Request($requestData);

        $response = $this->controller->update($request, $this->adminUser);

        $this->adminUser->refresh();
        $this->assertEquals('admin', $this->adminUser->role); // Should remain admin
        $this->assertEquals(302, $response->getStatusCode());
    }

    #[Test]
    public function update_prevents_self_account_locking()
    {
        // Mock the authenticated user
        Auth::shouldReceive('id')->andReturn($this->adminUser->id);

        $requestData = [
            'name' => $this->adminUser->name,
            'email' => $this->adminUser->email,
            'username' => $this->adminUser->username,
            'role' => $this->adminUser->role,
            'account_status' => 'locked', // Trying to lock own account
        ];
        $request = new Request($requestData);

        $response = $this->controller->update($request, $this->adminUser);

        $this->adminUser->refresh();
        $this->assertFalse($this->adminUser->isLocked());
        $this->assertEquals(302, $response->getStatusCode());
    }

    #[Test]
    public function update_validates_unique_email_ignoring_current_user()
    {
        $user1 = User::factory()->create(['email' => '<EMAIL>']);
        $user2 = User::factory()->create(['email' => '<EMAIL>']);

        // Try to update user1 with user2's email
        $requestData = [
            'name' => $user1->name,
            'email' => '<EMAIL>', // Duplicate email
            'username' => $user1->username,
            'role' => $user1->role,
            'account_status' => 'active',
        ];
        $request = new Request($requestData);

        $response = $this->controller->update($request, $user1);

        // Should redirect back with errors
        $this->assertEquals(302, $response->getStatusCode());

        $user1->refresh();
        $this->assertEquals('<EMAIL>', $user1->email); // Should remain unchanged
    }

    #[Test]
    public function update_allows_keeping_same_email()
    {
        $user = User::factory()->create([
            'role' => 'employee',
            'email' => '<EMAIL>',
            'name' => 'Original Name',
        ]);

        $requestData = [
            'name' => 'Updated Name',
            'email' => '<EMAIL>', // Same email
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'active',
        ];
        $request = new Request($requestData);

        $response = $this->controller->update($request, $user);

        $user->refresh();
        $this->assertEquals('Updated Name', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals(302, $response->getStatusCode());
    }

    #[Test]
    public function destroy_deletes_user_successfully()
    {
        $userToDelete = User::factory()->create();
        $userName = $userToDelete->name;

        // Mock authenticated user as different user
        Auth::shouldReceive('id')->andReturn($this->adminUser->id);

        $response = $this->controller->destroy($userToDelete);

        $this->assertDatabaseMissing('users', ['id' => $userToDelete->id]);
        $this->assertEquals(302, $response->getStatusCode());
    }

    #[Test]
    public function destroy_prevents_self_deletion()
    {
        // Mock the authenticated user
        Auth::shouldReceive('id')->andReturn($this->adminUser->id);

        $response = $this->controller->destroy($this->adminUser);

        // User should still exist
        $this->assertDatabaseHas('users', ['id' => $this->adminUser->id]);
        $this->assertEquals(302, $response->getStatusCode());
    }

    #[Test]
    public function lock_locks_user_account_successfully()
    {
        $userToLock = User::factory()->create([
            'locked_until' => null,
            'failed_login_attempts' => 0,
        ]);

        // Mock authenticated user as different user
        Auth::shouldReceive('id')->andReturn($this->adminUser->id);

        $response = $this->controller->lock($userToLock);

        $userToLock->refresh();
        $this->assertTrue($userToLock->isLocked());
        $this->assertEquals(5, $userToLock->failed_login_attempts);
        $this->assertEquals(302, $response->getStatusCode());
    }

    #[Test]
    public function lock_prevents_self_locking()
    {
        // Mock the authenticated user
        Auth::shouldReceive('id')->andReturn($this->adminUser->id);

        $response = $this->controller->lock($this->adminUser);

        $this->adminUser->refresh();
        $this->assertFalse($this->adminUser->isLocked());
        $this->assertEquals(302, $response->getStatusCode());
    }

    #[Test]
    public function unlock_unlocks_user_account_successfully()
    {
        $userToUnlock = User::factory()->create([
            'locked_until' => now()->addHours(24),
            'failed_login_attempts' => 5,
        ]);

        $response = $this->controller->unlock($userToUnlock);

        $userToUnlock->refresh();
        $this->assertFalse($userToUnlock->isLocked());
        $this->assertEquals(0, $userToUnlock->failed_login_attempts);
        $this->assertNull($userToUnlock->locked_until);
        $this->assertEquals(302, $response->getStatusCode());
    }

    #[Test]
    public function store_validates_minimum_password_length()
    {
        $userData = [
            'name' => 'Short Password User',
            'email' => '<EMAIL>',
            'password' => '1234567', // Only 7 characters
            'password_confirmation' => '1234567',
            'role' => 'user',
        ];

        $request = new Request($userData);
        $response = $this->controller->store($request);

        // Should redirect back with errors
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    #[Test]
    public function store_validates_email_format()
    {
        $userData = [
            'name' => 'Invalid Email User',
            'email' => 'not-an-email', // Invalid email format
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'user',
        ];

        $request = new Request($userData);
        $response = $this->controller->store($request);

        // Should redirect back with errors
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseMissing('users', ['email' => 'not-an-email']);
    }

    #[Test]
    public function store_validates_name_max_length()
    {
        $userData = [
            'name' => str_repeat('a', 256), // 256 characters, exceeds max of 255
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'user',
        ];

        $request = new Request($userData);
        $response = $this->controller->store($request);

        // Should redirect back with errors
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    #[Test]
    public function update_validates_password_confirmation_when_password_provided()
    {
        $user = User::factory()->create();

        $requestData = [
            'name' => $user->name,
            'email' => $user->email,
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'active',
            'password' => 'newpassword123',
            'password_confirmation' => 'differentpassword', // Mismatch
        ];
        $request = new Request($requestData);

        $response = $this->controller->update($request, $user);

        // Should redirect back with errors
        $this->assertEquals(302, $response->getStatusCode());
    }

    #[Test]
    public function update_validates_account_status_values()
    {
        $user = User::factory()->create();

        $requestData = [
            'name' => $user->name,
            'email' => $user->email,
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'invalid_status', // Invalid status
        ];
        $request = new Request($requestData);

        $response = $this->controller->update($request, $user);

        // Should redirect back with errors
        $this->assertEquals(302, $response->getStatusCode());
    }

    #[Test]
    public function update_handles_boolean_reset_failed_attempts()
    {
        $user = User::factory()->create([
            'role' => 'employee',
            'email' => '<EMAIL>',
            'name' => 'Test User 6',
            'failed_login_attempts' => 3,
        ]);

        // Test with boolean true (Laravel's Request::boolean() expects 1, '1', 'true', 'on', 'yes')
        $requestData = [
            'name' => $user->name,
            'email' => $user->email,
            'username' => $user->username,
            'role' => $user->role,
            'account_status' => 'active',
            'reset_failed_attempts' => '1',
        ];
        $request = new Request($requestData);

        $response = $this->controller->update($request, $user);

        $user->refresh();
        $this->assertEquals(0, $user->failed_login_attempts);
    }

    #[Test]
    public function lock_sets_correct_lock_duration()
    {
        $userToLock = User::factory()->create();

        // Mock authenticated user as different user
        Auth::shouldReceive('id')->andReturn($this->adminUser->id);

        $beforeLock = now();
        $response = $this->controller->lock($userToLock);
        $afterLock = now();

        $userToLock->refresh();

        // Check that lock duration is approximately 24 hours
        $this->assertTrue($userToLock->locked_until->between(
            $beforeLock->addHours(23)->addMinutes(59),
            $afterLock->addHours(24)->addMinute()
        ));
    }

    #[Test]
    public function store_creates_all_valid_role_types()
    {
        $roles = ['admin', 'employee', 'user'];

        foreach ($roles as $role) {
            $userData = [
                'name' => ucfirst($role).' User',
                'email' => $role.'@test.com',
                'password' => 'password123',
                'password_confirmation' => 'password123',
                'role' => $role,
            ];

            $request = new Request($userData);
            $response = $this->controller->store($request);

            $this->assertDatabaseHas('users', [
                'email' => $role.'@test.com',
                'role' => $role,
            ]);
        }
    }

    #[Test]
    public function update_role_accepts_all_valid_role_types()
    {
        $user = User::factory()->create(['role' => 'user']);
        $validRoles = ['admin', 'employee', 'user'];

        foreach ($validRoles as $role) {
            $requestData = ['role' => $role];
            $request = new Request($requestData);

            $response = $this->controller->updateRole($request, $user);

            $user->refresh();
            $this->assertEquals($role, $user->role);
        }
    }

    #[Test]
    public function controller_methods_return_correct_response_types()
    {
        $user = User::factory()->create();

        // Test view responses
        $indexResponse = $this->controller->index();
        $this->assertInstanceOf(\Illuminate\View\View::class, $indexResponse);

        $createResponse = $this->controller->create();
        $this->assertInstanceOf(\Illuminate\View\View::class, $createResponse);

        $showResponse = $this->controller->show($user);
        $this->assertInstanceOf(\Illuminate\View\View::class, $showResponse);

        $editResponse = $this->controller->edit($user);
        $this->assertInstanceOf(\Illuminate\View\View::class, $editResponse);
    }
}
