<?php

namespace Tests\Unit\Model;

use App\Models\Booking;
use App\Models\Reservation;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(User::class)]
class UserModelTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function user_has_correct_fillable_attributes()
    {
        $user = new User;
        $fillable = $user->getFillable();

        $expectedFillable = [
            'name',
            'email',
            'username',
            'password',
            'role',
            'failed_login_attempts',
            'locked_until',
            'email_verified_at',
        ];

        foreach ($expectedFillable as $attribute) {
            $this->assertContains($attribute, $fillable);
        }
    }

    #[Test]
    public function user_has_correct_hidden_attributes()
    {
        $user = new User;
        $hidden = $user->getHidden();

        $this->assertContains('password', $hidden);
        $this->assertContains('remember_token', $hidden);
    }

    #[Test]
    public function user_has_correct_casts()
    {
        $user = User::factory()->create([
            'email_verified_at' => '2024-01-01 10:00:00',
            'locked_until' => '2024-01-01 12:00:00',
            'password' => 'test-password',
        ]);

        // Test datetime casting
        $this->assertInstanceOf(\Carbon\Carbon::class, $user->email_verified_at);
        $this->assertInstanceOf(\Carbon\Carbon::class, $user->locked_until);

        // Test password hashing
        $this->assertTrue(Hash::check('test-password', $user->password));
    }

    #[Test]
    public function user_can_be_created_with_factory()
    {
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'user',
        ]);

        $this->assertNotNull($user->id);
        $this->assertEquals('Test User', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('user', $user->role);
        $this->assertNotNull($user->email_verified_at);
    }

    #[Test]
    public function is_admin_returns_true_for_admin_role()
    {
        $adminUser = User::factory()->create(['role' => 'admin']);
        $regularUser = User::factory()->create(['role' => 'user']);

        $this->assertTrue($adminUser->isAdmin());
        $this->assertFalse($regularUser->isAdmin());
    }

    #[Test]
    public function is_employee_returns_true_for_employee_role()
    {
        $employeeUser = User::factory()->create(['role' => 'employee']);
        $regularUser = User::factory()->create(['role' => 'user']);

        $this->assertTrue($employeeUser->isEmployee());
        $this->assertFalse($regularUser->isEmployee());
    }

    #[Test]
    public function is_user_returns_true_for_user_role()
    {
        $regularUser = User::factory()->create(['role' => 'user']);
        $adminUser = User::factory()->create(['role' => 'admin']);

        $this->assertTrue($regularUser->isUser());
        $this->assertFalse($adminUser->isUser());
    }

    #[Test]
    public function is_normal_user_is_deprecated_but_works_for_employee_role()
    {
        $employeeUser = User::factory()->create(['role' => 'employee']);
        $regularUser = User::factory()->create(['role' => 'user']);

        $this->assertTrue($employeeUser->isNormalUser());
        $this->assertFalse($regularUser->isNormalUser());
    }

    #[Test]
    public function is_client_is_deprecated_but_works_for_user_role()
    {
        $regularUser = User::factory()->create(['role' => 'user']);
        $employeeUser = User::factory()->create(['role' => 'employee']);

        $this->assertTrue($regularUser->isClient());
        $this->assertFalse($employeeUser->isClient());
    }

    #[Test]
    public function is_locked_returns_true_when_locked_until_is_in_future()
    {
        $lockedUser = User::factory()->create([
            'locked_until' => now()->addMinutes(30),
        ]);
        $unlockedUser = User::factory()->create([
            'locked_until' => now()->subMinutes(30),
        ]);
        $neverLockedUser = User::factory()->create([
            'locked_until' => null,
        ]);

        $this->assertTrue($lockedUser->isLocked());
        $this->assertFalse($unlockedUser->isLocked());
        $this->assertFalse($neverLockedUser->isLocked());
    }

    #[Test]
    public function increment_failed_attempts_increases_counter()
    {
        $user = User::factory()->create(['failed_login_attempts' => 2]);

        $user->incrementFailedAttempts();

        $this->assertEquals(3, $user->fresh()->failed_login_attempts);
    }

    #[Test]
    public function increment_failed_attempts_locks_user_after_five_attempts()
    {
        $user = User::factory()->create(['failed_login_attempts' => 4]);

        $user->incrementFailedAttempts();

        $freshUser = $user->fresh();
        $this->assertEquals(5, $freshUser->failed_login_attempts);
        $this->assertNotNull($freshUser->locked_until);
        $this->assertTrue($freshUser->locked_until->isFuture());
    }

    #[Test]
    public function reset_failed_attempts_clears_counter_and_lock()
    {
        $user = User::factory()->create([
            'failed_login_attempts' => 5,
            'locked_until' => now()->addMinutes(30),
        ]);

        $user->resetFailedAttempts();

        $freshUser = $user->fresh();
        $this->assertEquals(0, $freshUser->failed_login_attempts);
        $this->assertNull($freshUser->locked_until);
    }

    #[Test]
    public function bookings_relationship_returns_has_many()
    {
        $user = User::factory()->create();

        $relationship = $user->bookings();

        $this->assertInstanceOf(HasMany::class, $relationship);
        $this->assertEquals('user_id', $relationship->getForeignKeyName());
    }

    #[Test]
    public function admin_bookings_relationship_returns_has_many_with_custom_foreign_key()
    {
        $user = User::factory()->create();

        $relationship = $user->adminBookings();

        $this->assertInstanceOf(HasMany::class, $relationship);
        $this->assertEquals('booked_by', $relationship->getForeignKeyName());
    }

    #[Test]
    public function reservations_relationship_returns_has_many()
    {
        $user = User::factory()->create();

        $relationship = $user->reservations();

        $this->assertInstanceOf(HasMany::class, $relationship);
        $this->assertEquals('user_id', $relationship->getForeignKeyName());
    }

    #[Test]
    public function bookings_relationship_returns_collection_of_booking_objects()
    {
        $user = User::factory()->create();
        $booking1 = Booking::factory()->create(['user_id' => $user->id]);
        $booking2 = Booking::factory()->create(['user_id' => $user->id]);

        $bookings = $user->bookings;

        $this->assertInstanceOf(Collection::class, $bookings);
        $this->assertCount(2, $bookings);
        $this->assertInstanceOf(Booking::class, $bookings->first());
        $this->assertTrue($bookings->contains($booking1));
        $this->assertTrue($bookings->contains($booking2));
    }

    #[Test]
    public function admin_bookings_relationship_returns_bookings_made_by_admin()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $regularUser = User::factory()->create(['role' => 'user']);

        $adminBooking = Booking::factory()->create([
            'user_id' => $regularUser->id,
            'booked_by' => $admin->id,
        ]);

        $regularBooking = Booking::factory()->create([
            'user_id' => $regularUser->id,
            'booked_by' => null,
        ]);

        $adminBookings = $admin->adminBookings;

        $this->assertInstanceOf(Collection::class, $adminBookings);
        $this->assertCount(1, $adminBookings);
        $this->assertTrue($adminBookings->contains($adminBooking));
        $this->assertFalse($adminBookings->contains($regularBooking));
    }

    #[Test]
    public function reservations_relationship_returns_collection_of_reservation_objects()
    {
        $user = User::factory()->create();
        $reservation1 = Reservation::factory()->create(['user_id' => $user->id]);
        $reservation2 = Reservation::factory()->create(['user_id' => $user->id]);

        $reservations = $user->reservations;

        $this->assertInstanceOf(Collection::class, $reservations);
        $this->assertCount(2, $reservations);
        $this->assertInstanceOf(Reservation::class, $reservations->first());
        $this->assertTrue($reservations->contains($reservation1));
        $this->assertTrue($reservations->contains($reservation2));
    }

    #[Test]
    public function upcoming_reservations_relationship_returns_has_many_with_scopes()
    {
        $user = User::factory()->create();

        $relationship = $user->upcomingReservations();

        $this->assertInstanceOf(HasMany::class, $relationship);
        $this->assertEquals('user_id', $relationship->getForeignKeyName());
    }

    #[Test]
    public function upcoming_reservations_returns_only_future_active_reservations()
    {
        $user = User::factory()->create();

        // Create upcoming active reservation
        $upcomingReservation = Reservation::factory()->create([
            'user_id' => $user->id,
            'booking_date' => now()->addDays(1),
            'status' => 'Confirmed',
        ]);

        // Create past reservation
        $pastReservation = Reservation::factory()->create([
            'user_id' => $user->id,
            'booking_date' => now()->subDays(1),
            'status' => 'Confirmed',
        ]);

        // Create cancelled upcoming reservation
        $cancelledReservation = Reservation::factory()->create([
            'user_id' => $user->id,
            'booking_date' => now()->addDays(2),
            'status' => 'Cancelled',
        ]);

        $upcomingReservations = $user->upcomingReservations;

        $this->assertInstanceOf(Collection::class, $upcomingReservations);
        $this->assertCount(1, $upcomingReservations);
        $this->assertTrue($upcomingReservations->contains($upcomingReservation));
        $this->assertFalse($upcomingReservations->contains($pastReservation));
        $this->assertFalse($upcomingReservations->contains($cancelledReservation));
    }

    #[Test]
    public function todays_reservations_relationship_returns_has_many_with_scopes()
    {
        $user = User::factory()->create();

        $relationship = $user->todaysReservations();

        $this->assertInstanceOf(HasMany::class, $relationship);
        $this->assertEquals('user_id', $relationship->getForeignKeyName());
    }

    #[Test]
    public function todays_reservations_returns_only_todays_active_reservations()
    {
        $user = User::factory()->create();

        // Create today's active reservation
        $todaysReservation = Reservation::factory()->create([
            'user_id' => $user->id,
            'booking_date' => now()->toDateString(),
            'status' => 'Confirmed',
        ]);

        // Create tomorrow's reservation
        $tomorrowReservation = Reservation::factory()->create([
            'user_id' => $user->id,
            'booking_date' => now()->addDay()->toDateString(),
            'status' => 'Confirmed',
        ]);

        // Create today's cancelled reservation
        $cancelledTodayReservation = Reservation::factory()->create([
            'user_id' => $user->id,
            'booking_date' => now()->toDateString(),
            'status' => 'Cancelled',
        ]);

        $todaysReservations = $user->todaysReservations;

        $this->assertInstanceOf(Collection::class, $todaysReservations);
        $this->assertCount(1, $todaysReservations);
        $this->assertTrue($todaysReservations->contains($todaysReservation));
        $this->assertFalse($todaysReservations->contains($tomorrowReservation));
        $this->assertFalse($todaysReservations->contains($cancelledTodayReservation));
    }

    #[Test]
    public function user_can_be_created_with_minimal_required_data()
    {
        $user = User::create([
            'name' => 'Minimal User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'employee', // Explicitly set since role is required
        ]);

        $this->assertNotNull($user->id);
        $this->assertEquals('Minimal User', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('employee', $user->role);
        $this->assertEquals(0, $user->failed_login_attempts);
        $this->assertNull($user->locked_until);
    }

    #[Test]
    public function user_can_be_created_with_all_fillable_attributes()
    {
        $user = User::create([
            'name' => 'Complete User',
            'email' => '<EMAIL>',
            'username' => 'completeuser',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'failed_login_attempts' => 2,
            'locked_until' => now()->addMinutes(30),
            'email_verified_at' => now(),
        ]);

        $this->assertNotNull($user->id);
        $this->assertEquals('Complete User', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('completeuser', $user->username);
        $this->assertEquals('admin', $user->role);
        $this->assertEquals(2, $user->failed_login_attempts);
        $this->assertNotNull($user->locked_until);
        $this->assertNotNull($user->email_verified_at);
    }

    #[Test]
    public function user_password_is_properly_hidden_in_serialization()
    {
        $user = User::factory()->create(['password' => Hash::make('secret')]);

        $userArray = $user->toArray();

        $this->assertArrayNotHasKey('password', $userArray);
        $this->assertArrayNotHasKey('remember_token', $userArray);
    }

    #[Test]
    public function user_extends_authenticatable()
    {
        $user = new User;

        $this->assertInstanceOf(\Illuminate\Foundation\Auth\User::class, $user);
    }

    #[Test]
    public function user_uses_has_factory_trait()
    {
        $this->assertTrue(in_array(\Illuminate\Database\Eloquent\Factories\HasFactory::class, class_uses(User::class)));
    }

    #[Test]
    public function user_uses_notifiable_trait()
    {
        $this->assertTrue(in_array(\Illuminate\Notifications\Notifiable::class, class_uses(User::class)));
    }

    #[Test]
    public function lock_duration_is_thirty_minutes_after_five_failed_attempts()
    {
        $user = User::factory()->create(['failed_login_attempts' => 4]);
        $beforeLock = now();

        $user->incrementFailedAttempts();

        $afterLock = now();
        $freshUser = $user->fresh();

        $this->assertNotNull($freshUser->locked_until);
        $this->assertTrue($freshUser->locked_until->between($beforeLock->addMinutes(29), $afterLock->addMinutes(31)));
    }

    #[Test]
    public function user_role_methods_work_with_all_valid_roles()
    {
        $adminUser = User::factory()->create(['role' => 'admin']);
        $employeeUser = User::factory()->create(['role' => 'employee']);
        $regularUser = User::factory()->create(['role' => 'user']);

        // Test admin role
        $this->assertTrue($adminUser->isAdmin());
        $this->assertFalse($adminUser->isEmployee());
        $this->assertFalse($adminUser->isUser());

        // Test employee role
        $this->assertFalse($employeeUser->isAdmin());
        $this->assertTrue($employeeUser->isEmployee());
        $this->assertFalse($employeeUser->isUser());

        // Test user role
        $this->assertFalse($regularUser->isAdmin());
        $this->assertFalse($regularUser->isEmployee());
        $this->assertTrue($regularUser->isUser());
    }

    #[Test]
    public function deprecated_role_methods_work_correctly()
    {
        $employeeUser = User::factory()->create(['role' => 'employee']);
        $regularUser = User::factory()->create(['role' => 'user']);
        $adminUser = User::factory()->create(['role' => 'admin']);

        // Test deprecated isNormalUser method (should return true for employee)
        $this->assertTrue($employeeUser->isNormalUser());
        $this->assertFalse($regularUser->isNormalUser());
        $this->assertFalse($adminUser->isNormalUser());

        // Test deprecated isClient method (should return true for user)
        $this->assertTrue($regularUser->isClient());
        $this->assertFalse($employeeUser->isClient());
        $this->assertFalse($adminUser->isClient());
    }
}
